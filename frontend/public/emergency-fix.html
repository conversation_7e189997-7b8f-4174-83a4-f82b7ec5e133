<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Correção de Emergência - Sistema de Cobrança</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #1976d2;
            border-bottom: 2px solid #1976d2;
            padding-bottom: 10px;
        }
        .card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        .warning {
            background-color: #fff3e0;
            border-left: 4px solid #ff9800;
            padding: 15px;
            margin-bottom: 20px;
        }
        .success {
            background-color: #e8f5e9;
            border-left: 4px solid #4caf50;
            padding: 15px;
            margin-bottom: 20px;
            display: none;
        }
        .error {
            background-color: #ffebee;
            border-left: 4px solid #f44336;
            padding: 15px;
            margin-bottom: 20px;
            display: none;
        }
        button {
            background-color: #1976d2;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background-color: #1565c0;
        }
        button.danger {
            background-color: #f44336;
        }
        button.danger:hover {
            background-color: #d32f2f;
        }
        button.success {
            background-color: #4caf50;
        }
        button.success:hover {
            background-color: #388e3c;
        }
        .log-container {
            background-color: #f5f5f5;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            margin-top: 20px;
        }
        .log-entry {
            margin: 5px 0;
            padding: 5px;
            border-bottom: 1px solid #eee;
        }
        .log-entry.info {
            color: #1976d2;
        }
        .log-entry.success {
            color: #4caf50;
            display: block;
        }
        .log-entry.error {
            color: #f44336;
            display: block;
        }
        .login-test {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
        }
        input {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 100%;
            margin-bottom: 10px;
            font-size: 16px;
        }
        .password-update {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
        }
    </style>
</head>
<body>
    <h1>Correção de Emergência - Sistema de Cobrança</h1>

    <div class="card">
        <h2>Correção de Problemas de Login</h2>
        <div class="warning">
            <strong>Atenção!</strong> Esta página é destinada a corrigir problemas de login no sistema.
            Ao executar a correção, todos os dados do localStorage serão redefinidos para os valores padrão.
        </div>

        <div id="success-message" class="success">
            <strong>Sucesso!</strong> A correção foi aplicada com sucesso. Agora você pode tentar fazer login novamente.
        </div>

        <div id="error-message" class="error">
            <strong>Erro!</strong> <span id="error-details"></span>
        </div>

        <div style="display: flex; gap: 10px; margin-bottom: 15px;">
            <button id="fix-button" onclick="applyFix()">Aplicar Correção Básica</button>
            <button id="restore-button" onclick="restoreFullData()" style="background-color: #4caf50;">Restaurar Dados Completos</button>
            <button id="clean-button" onclick="cleanSystem()" style="background-color: #f44336;">Limpar Sistema (Manter Admin)</button>
            <a href="clear-users.html" style="background-color: #ff9800; color: white; text-decoration: none; padding: 10px 20px; border-radius: 4px; display: inline-block;">Limpar Apenas Usuários</a>
            <a href="reset-all-users.html" style="background-color: #9c27b0; color: white; text-decoration: none; padding: 10px 20px; border-radius: 4px; display: inline-block;">Redefinir Todos Usuários</a>
            <a href="direct-user-reset.html" style="background-color: #e91e63; color: white; text-decoration: none; padding: 10px 20px; border-radius: 4px; display: inline-block;">Redefinição Direta</a>
            <button class="success" onclick="goToLogin()">Ir para Login</button>
        </div>

        <div style="display: flex; gap: 10px; margin-bottom: 15px;">
            <button id="show-all-button" onclick="showAllUsersAndPasswords()" style="background-color: #ff9800;">Mostrar Todos os Usuários e Senhas</button>
            <button id="sync-button" onclick="forceSyncUsers()" style="background-color: #9c27b0; color: white;">Forçar Sincronização de Usuários</button>
        </div>

        <div class="log-container" id="log-container">
            <div class="log-entry info">Aguardando ação...</div>
        </div>
    </div>

    <div class="card login-test">
        <h2>Testar Login</h2>
        <p>Use este formulário para testar se as credenciais estão funcionando corretamente:</p>

        <div>
            <label for="test-email">Email:</label>
            <input type="email" id="test-email" placeholder="<EMAIL>">
        </div>

        <div>
            <label for="test-password">Senha:</label>
            <input type="password" id="test-password" placeholder="Senha">
        </div>

        <button onclick="testLogin()">Testar Credenciais</button>

        <div id="test-result" style="margin-top: 15px;"></div>
    </div>

    <div class="card password-update">
        <h2>Atualizar Senha</h2>
        <p>Use este formulário para atualizar a senha de um usuário:</p>

        <div>
            <label for="update-email">Email:</label>
            <input type="email" id="update-email" placeholder="<EMAIL>">
        </div>

        <div>
            <label for="update-password">Nova Senha:</label>
            <input type="password" id="update-password" placeholder="Nova senha">
        </div>

        <button onclick="updatePassword()">Atualizar Senha</button>

        <div id="update-result" style="margin-top: 15px;"></div>
    </div>

    <script src="./PasswordFixService.js"></script>
    <script>
        // Função para adicionar log
        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('log-container');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.textContent = message;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // Função para aplicar a correção
        function applyFix() {
            // Confirmar antes de aplicar a correção
            if (!confirm('Tem certeza que deseja aplicar a correção de emergência? Esta ação irá corrigir problemas de senha no sistema.')) {
                addLog('Correção cancelada pelo usuário.', 'info');
                return;
            }

            addLog('Iniciando correção de emergência...', 'info');

            try {
                const result = window.PasswordFixService.fixPasswordIssue();

                if (result.success) {
                    document.getElementById('success-message').style.display = 'block';
                    document.getElementById('error-message').style.display = 'none';
                    addLog('Correção aplicada com sucesso!', 'success');

                    // Habilitar botão de ir para login
                    document.querySelector('button.success').style.display = 'inline-block';
                } else {
                    document.getElementById('success-message').style.display = 'none';
                    document.getElementById('error-message').style.display = 'block';
                    document.getElementById('error-details').textContent = result.message;
                    addLog(`Erro: ${result.message}`, 'error');
                }
            } catch (error) {
                document.getElementById('success-message').style.display = 'none';
                document.getElementById('error-message').style.display = 'block';
                document.getElementById('error-details').textContent = error.message;
                addLog(`Erro inesperado: ${error.message}`, 'error');
            }
        }

        // Função para limpar o sistema (manter apenas admin)
        function cleanSystem() {
            // Confirmar antes de limpar o sistema
            if (!confirm('ATENÇÃO: Esta ação irá limpar todos os dados do sistema, exceto o usuário administrador. Todos os pedidos serão removidos. Esta ação NÃO pode ser desfeita. Deseja continuar?')) {
                addLog('Limpeza do sistema cancelada pelo usuário.', 'info');
                return;
            }

            // Pedir confirmação adicional devido à gravidade da operação
            if (!confirm('Confirme novamente: Todos os dados do sistema serão REMOVIDOS. Apenas o usuário admin permanecerá. Tem certeza absoluta?')) {
                addLog('Limpeza do sistema cancelada na confirmação secundária.', 'info');
                return;
            }

            addLog('Iniciando limpeza do sistema...', 'info');

            try {
                // 1. Obter dados atuais do localStorage
                const authTokensStr = localStorage.getItem('authTokens');
                const userInfoStr = localStorage.getItem('userInfo');

                // 2. Limpar localStorage
                addLog('Limpando localStorage...', 'info');
                localStorage.clear();

                // 3. Restaurar autenticação do admin
                addLog('Restaurando autenticação do admin...', 'info');
                if (authTokensStr) {
                    localStorage.setItem('authTokens', JSON.stringify(authTokensStr));
                } else {
                    // Criar tokens padrão para admin
                    const adminAuthTokens = {
                        access_token: "mock-admin-token",
                        refresh_token: "mock-admin-refresh-token",
                        token_type: "bearer"
                    };
                    localStorage.setItem('authTokens', JSON.stringify(adminAuthTokens));
                }

                if (userInfoStr) {
                    localStorage.setItem('userInfo', userInfoStr);
                } else {
                    // Criar informações padrão para admin
                    const adminUserInfo = {
                        id: 1,
                        email: "<EMAIL>",
                        fullName: "Admin",
                        role: "admin"
                    };
                    localStorage.setItem('userInfo', JSON.stringify(adminUserInfo));
                }

                // 4. Criar apenas o usuário admin
                addLog('Criando usuário admin...', 'info');

                // IMPORTANTE: Definir o usuário admin único que deve ser mantido
                const adminUser = {
                    id: 1,
                    nome: "Admin",
                    email: "<EMAIL>",
                    papeis: ["admin"],
                    permissoes: ["criar_usuario", "editar_usuario", "excluir_usuario", "ver_relatorios", "editar_configuracoes", "ver_todos_pedidos", "editar_pedidos"],
                    ativo: true
                };

                // Para o contexto principal - APENAS ADMIN
                localStorage.setItem('users', JSON.stringify([adminUser]));

                // Para o sistema de emergência - APENAS ADMIN
                const defaultUsers = {
                    '<EMAIL>': {
                        id: 1,
                        email: '<EMAIL>',
                        fullName: 'Admin',
                        role: 'admin',
                        password: 'admin123'
                    }
                };
                localStorage.setItem('default_users', JSON.stringify(defaultUsers));

                // Limpar senhas de usuários - APENAS ADMIN
                addLog('Limpando senhas de usuários...', 'info');
                const adminPasswords = {
                    '<EMAIL>': 'admin123'
                };
                localStorage.setItem('user_passwords', JSON.stringify(adminPasswords));

                // Limpar mock users - APENAS ADMIN
                addLog('Limpando mock users...', 'info');
                localStorage.setItem('mockUsers', JSON.stringify([{
                    id: 1,
                    nome: "Admin",
                    email: "<EMAIL>",
                    perfil: "Administrador",
                    ativo: true
                }]));

                // 5. Verificar e limpar todos os armazenamentos de usuários
                addLog('Verificando todos os armazenamentos de usuários...', 'info');

                // Lista de chaves específicas de usuários que verificamos explicitamente
                const checkedUserKeys = ['users', 'default_users', 'user_passwords', 'mockUsers', 'userInfo'];

                // Remover quaisquer outros armazenamentos de usuários
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    if (key && (
                        key.includes('user') ||
                        key.includes('User') ||
                        key.includes('usuario') ||
                        key.includes('Usuario') ||
                        key.includes('perfil') ||
                        key.includes('Perfil') ||
                        key.includes('role') ||
                        key.includes('Role') ||
                        key.includes('admin') ||
                        key.includes('Admin')
                    )) {
                        // Verificar se não é uma das chaves que já tratamos explicitamente
                        if (!checkedUserKeys.includes(key)) {
                            // Limpar ou definir para um array vazio
                            localStorage.setItem(key, JSON.stringify([]));
                            addLog(`Limpando armazenamento adicional de usuários: ${key}`, 'info');
                        }
                    }
                }

                // 6. Chamar explicitamente o UserSyncService para forçar a sincronização correta
                const syncScript = document.createElement('script');
                syncScript.textContent = `
                    // Forçar sincronização correta de usuários
                    try {
                        // Garantir que apenas o admin exista em todos os contextos
                        const adminUser = {
                            id: 1,
                            nome: "Admin",
                            email: "<EMAIL>",
                            papeis: ["admin"],
                            permissoes: ["criar_usuario", "editar_usuario", "excluir_usuario", "ver_relatorios", "editar_configuracoes", "ver_todos_pedidos", "editar_pedidos"],
                            ativo: true
                        };

                        localStorage.setItem('users', JSON.stringify([adminUser]));

                        // Verificar se o módulo UserSyncService está disponível
                        if (window.UserSyncService && typeof window.UserSyncService.syncAllUsers === 'function') {
                            window.UserSyncService.syncAllUsers([adminUser]);
                            console.log('UserSyncService encontrado e chamado para sincronizar usuários');
                        } else {
                            console.log('UserSyncService não disponível diretamente');
                        }

                        // Disparar evento personalizado para aplicativo React
                        window.dispatchEvent(new CustomEvent('user-data-reset', { detail: { users: [adminUser] } }));

                        // Disparar evento para forçar recarga de usuários
                        window.dispatchEvent(new CustomEvent('user-list-reset'));
                        console.log('Evento user-list-reset disparado');

                        console.log('Usuários redefinidos para apenas admin');
                    } catch (err) {
                        console.error('Erro ao forçar sincronização de usuários:', err);
                    }
                `;
                document.body.appendChild(syncScript);
                setTimeout(() => {
                    document.body.removeChild(syncScript);
                }, 1000);

                // 7. Remover completamente todos os dados de pedidos
                addLog('Limpando pedidos de todas as fontes...', 'info');
                localStorage.setItem('orders', JSON.stringify([]));
                localStorage.setItem('salesData', JSON.stringify([]));
                localStorage.setItem('recentOrders', JSON.stringify([]));
                localStorage.setItem('pendingOrders', JSON.stringify([]));
                localStorage.setItem('completedOrders', JSON.stringify([]));
                localStorage.setItem('trackingData', JSON.stringify([]));
                localStorage.setItem('orderHistory', JSON.stringify([]));
                localStorage.setItem('dashboard_data', JSON.stringify({}));
                localStorage.setItem('cached_orders', JSON.stringify([]));
                localStorage.setItem('last_orders', JSON.stringify([]));

                // Remover quaisquer outros itens que possam conter dados de pedidos
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    if (key && (
                        key.includes('order') ||
                        key.includes('Order') ||
                        key.includes('venda') ||
                        key.includes('Venda') ||
                        key.includes('pedido') ||
                        key.includes('Pedido')
                    )) {
                        localStorage.setItem(key, JSON.stringify([]));
                    }
                }

                // 8. Manter produtos
                addLog('Configurando produtos...', 'info');
                const sampleProducts = [
                    {
                        id: 1,
                        name: "Potencia Azul",
                        description: "Suplemento para saúde masculina",
                        active: true,
                        offers: [
                            {
                                id: 1,
                                name: "2 Gel + 2 Cápsulas",
                                description: "Combo completo",
                                price: 197.90,
                                active: true,
                                variation: "gel",
                                gelQuantity: 2,
                                capsulasQuantity: 2
                            },
                            {
                                id: 2,
                                name: "3 Gel",
                                description: "Tratamento intensivo gel",
                                price: 147.90,
                                active: true,
                                variation: "gel",
                                gelQuantity: 3,
                                capsulasQuantity: 0
                            },
                            {
                                id: 3,
                                name: "2 Cápsulas",
                                description: "Tratamento com cápsulas",
                                price: 97.90,
                                active: true,
                                variation: "capsulas",
                                gelQuantity: 0,
                                capsulasQuantity: 2
                            }
                        ]
                    }
                ];
                localStorage.setItem('products', JSON.stringify(sampleProducts));

                // 9. Adicionar um script para forçar a atualização do estado do app
                const script = document.createElement('script');
                script.textContent = `
                    // Tentar enviar um evento para o App.tsx para recarregar os pedidos
                    try {
                        window.dispatchEvent(new CustomEvent('emergency-fix-clear-orders'));
                        window.dispatchEvent(new CustomEvent('user-list-reset'));
                        console.log('Eventos de emergência disparados');
                    } catch (err) {
                        console.error('Erro ao disparar eventos:', err);
                    }
                `;
                document.body.appendChild(script);
                setTimeout(() => {
                    document.body.removeChild(script);
                }, 1000);

                // 10. Mostrar mensagem de sucesso
                document.getElementById('success-message').style.display = 'block';
                document.getElementById('error-message').style.display = 'none';
                addLog('Sistema limpo com sucesso! Apenas o admin foi mantido e todos os pedidos foram removidos.', 'success');

                // Habilitar botão de ir para login
                document.querySelector('button.success').style.display = 'inline-block';

                // Forçar recarga da página após 3 segundos para garantir que todas as alterações sejam aplicadas
                addLog('A página será recarregada em 3 segundos para aplicar todas as alterações...', 'info');
                setTimeout(() => {
                    window.location.reload();
                }, 3000);

                // 11. Exibir estado final
                showAllUsersAndPasswords();

            } catch (error) {
                document.getElementById('success-message').style.display = 'none';
                document.getElementById('error-message').style.display = 'block';
                document.getElementById('error-details').textContent = error.message;
                addLog(`Erro inesperado: ${error.message}`, 'error');
                console.error('Erro ao limpar sistema:', error);
            }
        }

        // Função para mostrar todos os usuários e senhas
        function showAllUsersAndPasswords() {
            // Confirmar antes de exibir informações sensíveis
            if (!confirm('Esta ação irá exibir informações sensíveis de todos os usuários, incluindo senhas. Deseja continuar?')) {
                addLog('Exibição de usuários e senhas cancelada pelo usuário.', 'info');
                return;
            }

            addLog('Coletando informações de todos os usuários e senhas...', 'info');

            try {
                const result = document.getElementById('update-result');
                result.innerHTML = '';

                // Criar container para os resultados
                const container = document.createElement('div');
                container.className = 'success';
                container.style.display = 'block';
                container.style.maxHeight = '500px';
                container.style.overflow = 'auto';
                container.style.padding = '15px';
                container.style.marginTop = '15px';
                container.style.backgroundColor = '#fff3e0';
                container.style.borderLeft = '4px solid #ff9800';

                // Título
                const title = document.createElement('h3');
                title.textContent = 'Todos os Usuários e Senhas';
                title.style.marginTop = '0';
                container.appendChild(title);

                // 1. Obter usuários do sistema de emergência (default_users)
                const defaultUsersStr = localStorage.getItem('default_users');
                const defaultUsers = defaultUsersStr ? JSON.parse(defaultUsersStr) : {};

                // 2. Obter usuários do contexto principal (users)
                const usersStr = localStorage.getItem('users');
                const users = usersStr ? JSON.parse(usersStr) : [];

                // 3. Obter senhas (user_passwords)
                const passwordsStr = localStorage.getItem('user_passwords');
                const passwords = passwordsStr ? JSON.parse(passwordsStr) : {};

                // 4. Obter mock users
                const mockUsersStr = localStorage.getItem('mockUsers');
                const mockUsers = mockUsersStr ? JSON.parse(mockUsersStr) : [];

                // 5. Obter usuário logado
                const userInfoStr = localStorage.getItem('userInfo');
                const userInfo = userInfoStr ? JSON.parse(userInfoStr) : null;

                // Criar tabela para exibir os resultados
                const table = document.createElement('table');
                table.style.width = '100%';
                table.style.borderCollapse = 'collapse';
                table.style.marginBottom = '20px';

                // Cabeçalho da tabela
                const thead = document.createElement('thead');
                const headerRow = document.createElement('tr');

                const headers = ['Email', 'Nome', 'Papel/Role', 'Senha', 'Locais Encontrados'];
                headers.forEach(headerText => {
                    const th = document.createElement('th');
                    th.textContent = headerText;
                    th.style.padding = '8px';
                    th.style.textAlign = 'left';
                    th.style.borderBottom = '2px solid #ddd';
                    headerRow.appendChild(th);
                });

                thead.appendChild(headerRow);
                table.appendChild(thead);

                // Corpo da tabela
                const tbody = document.createElement('tbody');

                // Mapa para rastrear todos os emails únicos
                const allEmails = new Set();

                // Adicionar emails de todas as fontes
                Object.keys(defaultUsers).forEach(email => allEmails.add(email));
                users.forEach(user => allEmails.add(user.email));
                Object.keys(passwords).forEach(email => allEmails.add(email));
                mockUsers.forEach(user => allEmails.add(user.email));
                if (userInfo && userInfo.email) allEmails.add(userInfo.email);

                // Converter para array e ordenar
                const emailsArray = Array.from(allEmails).sort();

                // Criar linhas para cada email
                emailsArray.forEach(email => {
                    const tr = document.createElement('tr');
                    tr.style.borderBottom = '1px solid #ddd';

                    // Destacar o usuário logado
                    if (userInfo && userInfo.email === email) {
                        tr.style.backgroundColor = '#e8f5e9';
                    }

                    // Coluna de email
                    const tdEmail = document.createElement('td');
                    tdEmail.textContent = email;
                    tdEmail.style.padding = '8px';
                    tr.appendChild(tdEmail);

                    // Coluna de nome
                    const tdName = document.createElement('td');
                    tdName.style.padding = '8px';

                    // Obter nome de todas as fontes possíveis
                    let name = '';
                    if (defaultUsers[email]) name = defaultUsers[email].fullName;
                    const userObj = users.find(u => u.email === email);
                    if (userObj) name = userObj.nome || name;
                    const mockUserObj = mockUsers.find(u => u.email === email);
                    if (mockUserObj) name = mockUserObj.nome || name;
                    if (userInfo && userInfo.email === email) name = userInfo.fullName || name;

                    tdName.textContent = name || '-';
                    tr.appendChild(tdName);

                    // Coluna de papel/role
                    const tdRole = document.createElement('td');
                    tdRole.style.padding = '8px';

                    // Obter papel/role de todas as fontes possíveis
                    let role = '';
                    if (defaultUsers[email]) role = defaultUsers[email].role;
                    if (userObj && userObj.papeis) role = userObj.papeis.join(', ');
                    if (mockUserObj) role = mockUserObj.perfil || role;
                    if (userInfo && userInfo.email === email) role = userInfo.role || role;

                    tdRole.textContent = role || '-';
                    tr.appendChild(tdRole);

                    // Coluna de senha
                    const tdPassword = document.createElement('td');
                    tdPassword.style.padding = '8px';

                    // Obter senha de todas as fontes possíveis
                    let password = '';
                    if (defaultUsers[email]) password = defaultUsers[email].password;
                    if (passwords[email]) password = passwords[email];

                    tdPassword.textContent = password || '-';
                    tr.appendChild(tdPassword);

                    // Coluna de locais encontrados
                    const tdLocations = document.createElement('td');
                    tdLocations.style.padding = '8px';

                    const locations = [];
                    if (defaultUsers[email]) locations.push('default_users');
                    if (userObj) locations.push('users');
                    if (passwords[email]) locations.push('user_passwords');
                    if (mockUserObj) locations.push('mockUsers');
                    if (userInfo && userInfo.email === email) locations.push('userInfo');

                    tdLocations.textContent = locations.join(', ') || '-';
                    tr.appendChild(tdLocations);

                    tbody.appendChild(tr);
                });

                table.appendChild(tbody);
                container.appendChild(table);

                // Adicionar resumo
                const summary = document.createElement('div');
                summary.innerHTML = `
                    <p><strong>Resumo:</strong></p>
                    <ul>
                        <li>Total de emails únicos: ${emailsArray.length}</li>
                        <li>Usuários em default_users: ${Object.keys(defaultUsers).length}</li>
                        <li>Usuários em users: ${users.length}</li>
                        <li>Senhas em user_passwords: ${Object.keys(passwords).length}</li>
                        <li>Usuários em mockUsers: ${mockUsers.length}</li>
                    </ul>
                `;
                container.appendChild(summary);

                // Adicionar ao resultado
                result.appendChild(container);

                // Adicionar log
                addLog(`Encontrados ${emailsArray.length} usuários únicos em todos os sistemas.`, 'success');

            } catch (error) {
                addLog(`Erro ao coletar informações: ${error.message}`, 'error');
                console.error('Erro ao coletar informações:', error);

                document.getElementById('update-result').innerHTML = `
                    <div class="error" style="display:block">
                        <strong>Erro ao coletar informações!</strong><br>
                        ${error.message}
                    </div>
                `;
            }
        }

        // Função para forçar a sincronização de usuários
        function forceSyncUsers() {
            addLog('Iniciando sincronização forçada de usuários...', 'info');

            try {
                // 1. Coletar todos os usuários de todas as fontes
                const allUsers = collectAllUsers();

                // 2. Unificar os usuários em um único array
                const unifiedUsers = unifyUsers(allUsers);

                // 3. Atualizar o users principal (contexto)
                localStorage.setItem('users', JSON.stringify(unifiedUsers));

                // 4. Simular a inicialização do UserSyncService
                // Isso atualizará automaticamente todos os outros locais de armazenamento
                const script = document.createElement('script');
                script.textContent = `
                    // Importar o serviço UserSyncService (simulação)
                    setTimeout(() => {
                        try {
                            // Obter os usuários do localStorage
                            const usersStr = localStorage.getItem('users');
                            const users = usersStr ? JSON.parse(usersStr) : [];

                            // Sincronizar com default_users
                            const defaultUsersStr = localStorage.getItem('default_users');
                            const defaultUsers = defaultUsersStr ? JSON.parse(defaultUsersStr) : {};

                            // Atualizar entradas para todos os usuários
                            users.forEach(user => {
                                // Mapear papel para role
                                let role = 'user';
                                if (user.papeis.includes('admin')) role = 'admin';
                                else if (user.papeis.includes('supervisor')) role = 'supervisor';
                                else if (user.papeis.includes('collector') || user.papeis.includes('operador')) role = 'collector';
                                else if (user.papeis.includes('seller') || user.papeis.includes('vendedor')) role = 'seller';

                                // Manter senha existente ou usar padrão
                                const existingPassword = defaultUsers[user.email]?.password || 'senha123';

                                defaultUsers[user.email] = {
                                    id: user.id,
                                    email: user.email,
                                    fullName: user.nome,
                                    role: role,
                                    password: existingPassword
                                };
                            });

                            localStorage.setItem('default_users', JSON.stringify(defaultUsers));

                            // Converter para o formato mockUsers
                            const mockUsers = users.map(user => {
                                // Determinar perfil
                                let perfil = 'Usuário';
                                if (user.papeis.includes('admin')) perfil = 'Administrador';
                                else if (user.papeis.includes('supervisor')) perfil = 'Supervisor';
                                else if (user.papeis.includes('collector') || user.papeis.includes('operador')) perfil = 'Operador';
                                else if (user.papeis.includes('seller') || user.papeis.includes('vendedor')) perfil = 'Vendedor';

                                return {
                                    id: user.id,
                                    nome: user.nome,
                                    email: user.email,
                                    perfil: perfil,
                                    permissoes: user.permissoes,
                                    ativo: user.ativo
                                };
                            });

                            localStorage.setItem('mockUsers', JSON.stringify(mockUsers));

                            // Sincronizar senhas se necessário
                            const passwordsStr = localStorage.getItem('user_passwords');
                            const passwords = passwordsStr ? JSON.parse(passwordsStr) : {};

                            // Verificar senhas nos default_users e adicioná-las se não existirem
                            users.forEach(user => {
                                // Se não existe senha para este usuário, mas existe no default_users
                                if (!passwords[user.email] && defaultUsers[user.email]?.password) {
                                    passwords[user.email] = defaultUsers[user.email].password;
                                }
                            });

                            localStorage.setItem('user_passwords', JSON.stringify(passwords));

                            console.log('Sincronização simulada concluída com sucesso!');
                        } catch (error) {
                            console.error('Erro durante a sincronização simulada:', error);
                        }
                    }, 100);
                `;
                document.body.appendChild(script);
                setTimeout(() => {
                    document.body.removeChild(script);
                }, 500);

                // 5. Mostrar resultado
                document.getElementById('update-result').innerHTML = `
                    <div class="success" style="display:block">
                        <strong>Sincronização concluída com sucesso!</strong><br>
                        ${unifiedUsers.length} usuários foram sincronizados em todos os sistemas.
                    </div>
                `;

                // 6. Mostrar os usuários sincronizados
                showAllUsersAndPasswords();

                addLog(`Sincronização concluída: ${unifiedUsers.length} usuários unificados.`, 'success');
            } catch (error) {
                addLog(`Erro na sincronização: ${error.message}`, 'error');
                document.getElementById('update-result').innerHTML = `
                    <div class="error" style="display:block">
                        <strong>Erro na sincronização!</strong><br>
                        ${error.message}
                    </div>
                `;
            }
        }

        // Função para coletar todos os usuários de todas as fontes
        function collectAllUsers() {
            const result = {
                defaultUsers: {},
                contextUsers: [],
                mockUsers: [],
                userInfo: null
            };

            // Obter usuários do sistema de emergência
            const defaultUsersStr = localStorage.getItem('default_users');
            if (defaultUsersStr) {
                result.defaultUsers = JSON.parse(defaultUsersStr);
            }

            // Obter usuários do contexto principal
            const usersStr = localStorage.getItem('users');
            if (usersStr) {
                result.contextUsers = JSON.parse(usersStr);
            }

            // Obter mock users
            const mockUsersStr = localStorage.getItem('mockUsers');
            if (mockUsersStr) {
                result.mockUsers = JSON.parse(mockUsersStr);
            }

            // Obter usuário logado
            const userInfoStr = localStorage.getItem('userInfo');
            if (userInfoStr) {
                result.userInfo = JSON.parse(userInfoStr);
            }

            return result;
        }

        // Função para unificar usuários de diferentes fontes
        function unifyUsers(allUsers) {
            const { defaultUsers, contextUsers, mockUsers, userInfo } = allUsers;

            // Mapa para rastrear todos os emails únicos
            const emailMap = new Map();

            // Adicionar usuários do contexto principal
            contextUsers.forEach(user => {
                if (user.email) {
                    emailMap.set(user.email.toLowerCase(), {
                        id: user.id,
                        nome: user.nome,
                        email: user.email,
                        papeis: user.papeis || [],
                        permissoes: user.permissoes || [],
                        ativo: user.ativo !== undefined ? user.ativo : true
                    });
                }
            });

            // Adicionar usuários do sistema de emergência
            Object.entries(defaultUsers).forEach(([email, userData]) => {
                const lowerEmail = email.toLowerCase();
                if (!emailMap.has(lowerEmail)) {
                    // Converter papel para papéis
                    const papeis = [];
                    if (userData.role === 'admin') papeis.push('admin');
                    else if (userData.role === 'supervisor') papeis.push('supervisor');
                    else if (userData.role === 'collector') papeis.push('collector');
                    else if (userData.role === 'seller') papeis.push('seller');

                    emailMap.set(lowerEmail, {
                        id: userData.id || Date.now(),
                        nome: userData.fullName || email.split('@')[0],
                        email: email,
                        papeis: papeis,
                        permissoes: getPermissoesByPapeis(papeis),
                        ativo: true
                    });
                }
            });

            // Adicionar mock users
            mockUsers.forEach(user => {
                if (user.email) {
                    const lowerEmail = user.email.toLowerCase();
                    if (!emailMap.has(lowerEmail)) {
                        // Converter perfil para papéis
                        const papeis = [];
                        if (user.perfil === 'Administrador') papeis.push('admin');
                        else if (user.perfil === 'Supervisor') papeis.push('supervisor');
                        else if (user.perfil === 'Operador') papeis.push('collector');
                        else if (user.perfil === 'Vendedor') papeis.push('seller');

                        emailMap.set(lowerEmail, {
                            id: user.id || Date.now(),
                            nome: user.nome || user.email.split('@')[0],
                            email: user.email,
                            papeis: papeis,
                            permissoes: getPermissoesByPapeis(papeis),
                            ativo: user.ativo !== undefined ? user.ativo : true
                        });
                    }
                }
            });

            // Adicionar usuário logado se não estiver na lista
            if (userInfo && userInfo.email) {
                const lowerEmail = userInfo.email.toLowerCase();
                if (!emailMap.has(lowerEmail)) {
                    // Converter role para papéis
                    const papeis = [];
                    if (userInfo.role === 'admin') papeis.push('admin');
                    else if (userInfo.role === 'supervisor') papeis.push('supervisor');
                    else if (userInfo.role === 'collector') papeis.push('collector');
                    else if (userInfo.role === 'seller') papeis.push('seller');

                    emailMap.set(lowerEmail, {
                        id: userInfo.id || Date.now(),
                        nome: userInfo.fullName || userInfo.email.split('@')[0],
                        email: userInfo.email,
                        papeis: papeis,
                        permissoes: getPermissoesByPapeis(papeis),
                        ativo: true
                    });
                }
            }

            // Converter o mapa para array
            return Array.from(emailMap.values());
        }

        // Função para obter permissões com base nos papéis
        function getPermissoesByPapeis(papeis) {
            const permissoes = [];

            if (papeis.includes('admin')) {
                permissoes.push('criar_usuario', 'editar_usuario', 'excluir_usuario', 'ver_relatorios', 'editar_configuracoes', 'ver_todos_pedidos', 'editar_pedidos');
            }

            if (papeis.includes('supervisor')) {
                permissoes.push('ver_relatorios', 'ver_todos_pedidos', 'editar_pedidos');
            }

            if (papeis.includes('collector')) {
                permissoes.push('ver_pedidos_atribuidos', 'editar_pedidos_atribuidos');
            }

            if (papeis.includes('seller')) {
                permissoes.push('ver_pedidos_proprios', 'criar_pedidos');
            }

            return Array.from(new Set(permissoes)); // Remove duplicatas
        }

        // Função para ir para a página de login
        function goToLogin() {
            window.location.href = '/login';
        }

        // Função para testar login
        function testLogin() {
            const email = document.getElementById('test-email').value;
            const password = document.getElementById('test-password').value;

            if (!email || !password) {
                document.getElementById('test-result').innerHTML = '<div class="error" style="display:block">Por favor, preencha email e senha.</div>';
                return;
            }

            const result = window.PasswordFixService.checkCredentials(email, password);

            if (result.valid) {
                document.getElementById('test-result').innerHTML = `
                    <div class="success" style="display:block">
                        <strong>Credenciais válidas!</strong><br>
                        Usuário: ${result.user.fullName}<br>
                        Função: ${result.user.role}
                    </div>
                `;
            } else {
                document.getElementById('test-result').innerHTML = `
                    <div class="error" style="display:block">
                        <strong>Credenciais inválidas!</strong><br>
                        ${result.message}
                    </div>
                `;
            }
        }

        // Função para atualizar senha
        function updatePassword() {
            const email = document.getElementById('update-email').value;
            const password = document.getElementById('update-password').value;

            if (!email || !password) {
                document.getElementById('update-result').innerHTML = '<div class="error" style="display:block">Por favor, preencha email e nova senha.</div>';
                return;
            }

            const result = window.PasswordFixService.updatePassword(email, password);

            if (result.success) {
                document.getElementById('update-result').innerHTML = `
                    <div class="success" style="display:block">
                        <strong>Senha atualizada com sucesso!</strong><br>
                        ${result.message}
                    </div>
                `;
            } else {
                document.getElementById('update-result').innerHTML = `
                    <div class="error" style="display:block">
                        <strong>Erro ao atualizar senha!</strong><br>
                        ${result.message}
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
