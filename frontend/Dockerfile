# Stage 1: Build the React app
FROM node:20-alpine AS builder

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci

# Copy source code
COPY . .

# Build the app
RUN npm run build

# Stage 2: Serve the static files
FROM node:20-alpine

WORKDIR /app

# Install serve globally
RUN npm install -g serve

# Copy the build folder from the builder stage
COPY --from=builder /app/build ./build

# The PORT will be provided by Railway
EXPOSE 3000

# Start serve with SPA mode enabled
# serve automatically reads PORT env var
CMD ["serve", "-s", "build"]