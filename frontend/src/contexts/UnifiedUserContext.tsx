import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { User } from '../types/User';
import api from '../services/api';
import UnifiedAuthService from '../services/UnifiedAuthService';

// Interface do contexto
interface UserContextType {
  users: User[];
  loading: boolean;
  error: string | null;
  getUserById: (id: string) => User | undefined;
  getUserByEmail: (email: string) => User | undefined;
  addUser: (user: Partial<User>) => Promise<User | undefined>;
  updateUser: (id: string, user: Partial<User>) => Promise<User | undefined>;
  deleteUser: (userId: string) => Promise<boolean>;
  refreshUsers: () => Promise<void>;
}

// Criação do contexto
const UnifiedUserContext = createContext<UserContextType | undefined>(undefined);

// Provider props
interface UnifiedUserProviderProps {
  children: ReactNode;
}

// Provider component
export const UnifiedUserProvider: React.FC<UnifiedUserProviderProps> = ({ children }) => {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load users from backend
  const loadUsers = async () => {
    if (!UnifiedAuthService.isAuthenticated()) {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      const response = await api.get('/users');
      // The backend returns { data: users[], meta: {...} }
      const backendUsers = response.data?.data || [];
      
      // Transform backend user format to match frontend expectations
      const transformedUsers = backendUsers.map((user: any) => ({
        ...user,
        nome: user.fullName || user.name || user.email, // Use fullName or email as display name
        ativo: user.isActive !== undefined ? user.isActive : true,
        papeis: [user.role], // Convert single role to array
        full_name: user.fullName || user.name || '',
        is_active: user.isActive !== undefined ? user.isActive : true,
      }));
      
      setUsers(transformedUsers);
    } catch (err: any) {
      console.error('Error loading users:', err);
      setError(err.response?.data?.message || 'Erro ao carregar usuários');
      setUsers([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadUsers();
  }, []);

  const getUserById = (id: string): User | undefined => {
    return users.find(user => user.id === id);
  };

  const getUserByEmail = (email: string): User | undefined => {
    return users.find(user => user.email === email);
  };

  const addUser = async (userData: Partial<User>): Promise<User | undefined> => {
    try {
      setError(null);
      const response = await api.post('/users', userData);
      const newUser = response.data.data;
      
      // Transform the new user to match frontend format
      const transformedUser = {
        ...newUser,
        nome: newUser.fullName || newUser.name || newUser.email,
        ativo: newUser.isActive !== undefined ? newUser.isActive : true,
        papeis: [newUser.role],
        full_name: newUser.fullName || newUser.name || '',
        is_active: newUser.isActive !== undefined ? newUser.isActive : true,
      };
      
      // Update local state
      setUsers(prevUsers => [...prevUsers, transformedUser]);
      
      return newUser;
    } catch (err: any) {
      console.error('Error adding user:', err);
      setError(err.response?.data?.message || 'Erro ao adicionar usuário');
      return undefined;
    }
  };

  const updateUser = async (id: string, userData: Partial<User>): Promise<User | undefined> => {
    try {
      setError(null);
      const response = await api.patch(`/users/${id}`, userData);
      const updatedUser = response.data.data;
      
      // Transform the updated user to match frontend format
      const transformedUser = {
        ...updatedUser,
        nome: updatedUser.fullName || updatedUser.name || updatedUser.email,
        ativo: updatedUser.isActive !== undefined ? updatedUser.isActive : true,
        papeis: [updatedUser.role],
        full_name: updatedUser.fullName || updatedUser.name || '',
        is_active: updatedUser.isActive !== undefined ? updatedUser.isActive : true,
      };
      
      // Update local state
      setUsers(prevUsers => 
        prevUsers.map(user => user.id === id ? transformedUser : user)
      );
      
      return updatedUser;
    } catch (err: any) {
      console.error('Error updating user:', err);
      setError(err.response?.data?.message || 'Erro ao atualizar usuário');
      return undefined;
    }
  };

  const deleteUser = async (userId: string): Promise<boolean> => {
    try {
      setError(null);
      await api.delete(`/users/${userId}`);
      
      // Update local state
      setUsers(prevUsers => prevUsers.filter(user => user.id !== userId));
      
      return true;
    } catch (err: any) {
      console.error('Error deleting user:', err);
      setError(err.response?.data?.message || 'Erro ao deletar usuário');
      return false;
    }
  };

  const refreshUsers = async (): Promise<void> => {
    await loadUsers();
  };

  const contextValue: UserContextType = {
    users,
    loading,
    error,
    getUserById,
    getUserByEmail,
    addUser,
    updateUser,
    deleteUser,
    refreshUsers,
  };

  return (
    <UnifiedUserContext.Provider value={contextValue}>
      {children}
    </UnifiedUserContext.Provider>
  );
};

// Hook para usar o contexto
export const useUnifiedUsers = (): UserContextType => {
  const context = useContext(UnifiedUserContext);
  if (context === undefined) {
    throw new Error('useUnifiedUsers must be used within a UnifiedUserProvider');
  }
  return context;
};

export default UnifiedUserContext;