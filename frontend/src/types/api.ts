// API response types that match the backend models

// User types
export interface ApiUser {
  id: string;
  email: string;
  fullName: string;
  role: 'ADMIN' | 'SUPERVISOR' | 'COLLECTOR' | 'SELLER';
  isActive: boolean;
  tenantId: string;
}

// Order status enum - matching backend
export enum OrderStatus {
  PENDING = 'PENDING',
  IN_PROGRESS = 'IN_PROGRESS',
  PAID = 'PAID',
  PARTIALLY_PAID = 'PARTIALLY_PAID',
  NEGOTIATING = 'NEGOTIATING',
  CANCELLED = 'CANCELLED',
  DELIVERED = 'DELIVERED',
  DELETED = 'DELETED',
  LIBERACAO = 'LIBERACAO'
}

// Order types
export interface ApiBillingHistory {
  id: string;
  orderId: string;
  amount: number;
  notes: string | null;
  createdAt: string;
  createdBy: string;
  createdByName?: string; // This might be populated by the backend
}

export interface ApiOrder {
  id: string;
  orderNumber: string;
  customerName: string;
  customerPhone: string;
  customerAddress: string;
  totalAmount: number;
  paidAmount: number;
  status: OrderStatus;
  trackingCode: string | null;
  isDuplicate: boolean;
  tenantId: string;
  sellerId: string;
  collectorId: string | null;
  offerId: string | null;
  createdAt: string;
  updatedAt: string | null;
  deletedAt: string | null;
  billingHistory?: ApiBillingHistory[];

  // These fields might be populated by the backend
  sellerName?: string;
  collectorName?: string;
}

export interface ApiOrderCreate {
  orderNumber: string;
  customerName: string;
  customerPhone: string;
  customerAddress: string;
  totalAmount: number;
  trackingCode?: string | null;
  sellerId: string;
}

export interface ApiOrderUpdate {
  customerName?: string;
  customerPhone?: string;
  customerAddress?: string;
  totalAmount?: number;
  paidAmount?: number;
  status?: OrderStatus;
  trackingCode?: string | null;
  collectorId?: string | null;
  isDuplicate?: boolean;
}

export interface ApiBillingHistoryCreate {
  orderId: string;
  amount: number;
  notes?: string | null;
}

// Webhook types
export interface ApiWebhookOrder {
  order_number: string;
  customer_name: string;
  customer_phone: string;
  customer_address: string;
  total_amount: number;
  seller_id: number;
  tracking_code?: string | null;
}

// Statistics types
export interface ApiOrderStatistics {
  total_orders: number;
  total_amount: number;
  total_paid: number;
  payment_rate: number;
  status_counts: Record<OrderStatus, number>;
}

// Tracking types
export interface ApiTrackingUpdate {
  order_id: string;  // Changed to string to match Order.idVenda
  tracking_code: string;
  status: string;
  last_update: string;
  location?: string;
  estimated_delivery?: string;
  is_critical?: boolean;
}
