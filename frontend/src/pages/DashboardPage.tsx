import React, { useState, useEffect } from 'react';
import { useOutletContext } from 'react-router-dom';
import {
  Box,
  Grid,
  Paper,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  IconButton,
  Dialog,
  Button,
  Card,
  CardContent,
  TextField,
  Divider,
  LinearProgress,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  SelectChangeEvent,
  Tooltip,
} from '@mui/material';
import ShoppingCartIcon from '@mui/icons-material/ShoppingCart';
import AttachMoneyIcon from '@mui/icons-material/AttachMoney';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import WhatsAppIcon from '@mui/icons-material/WhatsApp';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import UploadFileIcon from '@mui/icons-material/UploadFile';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import { FilterList as FilterListIcon, Refresh as RefreshIcon, Close as CloseIcon } from '@mui/icons-material';
import PersonAddIcon from '@mui/icons-material/PersonAdd';
import PeopleIcon from '@mui/icons-material/People';
import CSVImport from '../components/CSVImport';
import { Order } from '../types/Order';
import OrdersTable from '../components/OrdersTable';
import OrdersTableWithPagination from '../components/OrdersTableWithPagination';
import ImportDialog from '../components/ImportDialog';
import { StatusFilter } from '../components/Sidebar';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { ptBR } from 'date-fns/locale';
import UnifiedAuthService from '../services/UnifiedAuthService';
import { useOrderData } from '../contexts/OrderDataContext';
import { useConversion } from '../contexts/ConversionContext';

const OverviewCard = ({ title, value, icon, color, subtitle = '', onClick = null, customValueStyle = {}, showProgressBar = false, progressValue = 0 }: any) => (
  <Card
    sx={{
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      background: '#FFFFFF',
      boxShadow: '0 2px 8px rgba(0,0,0,0.08)',
      borderRadius: '14px',
      border: 'none',
      transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
      cursor: onClick ? 'pointer' : 'default',
      position: 'relative',
      overflow: 'hidden',
      '&:hover': {
        transform: onClick ? 'translateY(-4px)' : 'none',
        boxShadow: onClick ? '0 4px 20px rgba(0,0,0,0.12)' : '0 2px 8px rgba(0,0,0,0.08)',
      },
      '&::before': {
        content: '""',
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        height: '3px',
        background: `linear-gradient(90deg, ${color === 'primary' ? '#2196F3' : color === 'success' ? '#4CAF50' : color === 'info' ? '#00BCD4' : '#FF9800'} 0%, ${color === 'primary' ? '#1976D2' : color === 'success' ? '#388E3C' : color === 'info' ? '#0097A7' : '#F57C00'} 100%)`,
      }
    }}
    onClick={onClick}
  >
    <CardContent sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column', p: 2.5 }}>
      <Box sx={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between', mb: 2.5 }}>
        <Box sx={{ flex: 1 }}>
          <Typography variant="body2" sx={{ 
            color: 'text.secondary',
            fontWeight: 500,
            fontSize: '0.75rem',
            letterSpacing: '0.5px',
            mb: 0.75,
            textTransform: 'uppercase'
          }}>
            {title}
          </Typography>
          <Typography variant="h4" component="div" sx={{ 
            fontWeight: 700,
            fontSize: '1.8rem',
            lineHeight: 1.2,
            color: '#1a1a1a',
            ...customValueStyle 
          }}>
            {value}
          </Typography>
        </Box>
        <Box sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          width: 42,
          height: 42,
          borderRadius: '10px',
          background: color === 'primary' ? 'linear-gradient(135deg, #E3F2FD 0%, #BBDEFB 100%)' : 
                      color === 'success' ? 'linear-gradient(135deg, #E8F5E9 0%, #C8E6C9 100%)' :
                      color === 'info' ? 'linear-gradient(135deg, #E0F2F1 0%, #B2DFDB 100%)' :
                      'linear-gradient(135deg, #FFF3E0 0%, #FFE0B2 100%)',
          color: color === 'primary' ? '#1976D2' : 
                 color === 'success' ? '#388E3C' :
                 color === 'info' ? '#0097A7' :
                 '#F57C00',
          '& .MuiSvgIcon-root': {
            fontSize: '1.35rem'
          }
        }}>
          {icon}
        </Box>
      </Box>
      {subtitle && (
        <Typography variant="body2" sx={{ 
          color: 'text.secondary',
          fontSize: '0.75rem',
          mt: 'auto'
        }}>
          {subtitle}
        </Typography>
      )}
      {showProgressBar && (
        <Box sx={{ mt: 1.8, width: '100%' }}>
          <LinearProgress
            variant="determinate"
            value={progressValue}
            sx={{
              height: 5,
              borderRadius: 2.5,
              bgcolor: 'rgba(0,0,0,0.08)',
              '& .MuiLinearProgress-bar': {
                borderRadius: 2.5,
                background: color === 'primary' ? 'linear-gradient(90deg, #2196F3 0%, #1976D2 100%)' : 
                           color === 'success' ? 'linear-gradient(90deg, #4CAF50 0%, #388E3C 100%)' :
                           color === 'info' ? 'linear-gradient(90deg, #00BCD4 0%, #0097A7 100%)' :
                           'linear-gradient(90deg, #FF9800 0%, #F57C00 100%)',
              }
            }}
          />
        </Box>
      )}
    </CardContent>
  </Card>
);

const StatusChip = ({ status }: { status: string }) => {
  const getStatusConfig = (status: string) => {
    const configs: Record<string, { color: 'primary' | 'success' | 'error'; icon: React.ReactElement }> = {
      'Pendente': { color: 'primary', icon: <AccessTimeIcon fontSize="small" /> },
      'Completo': { color: 'success', icon: <CheckCircleIcon fontSize="small" /> },
      'Entrega Falha': { color: 'error', icon: <ErrorIcon fontSize="small" /> },
    };
    return configs[status] || configs['Pendente'];
  };

  const config = getStatusConfig(status);
  return (
    <Chip
      icon={config.icon}
      label={status}
      color={config.color}
      size="small"
      variant="filled"
      sx={{
        height: '22px',
        borderRadius: '6px',
        fontSize: '0.7rem',
        '& .MuiChip-label': {
          px: 0.75,
          fontWeight: 600,
          fontSize: '0.7rem',
        },
        '& .MuiChip-icon': {
          fontSize: '0.9rem',
        },
      }}
    />
  );
};

// Type for the outlet context from Layout
interface LayoutOutletContext {
  selectedStatus: StatusFilter | null;
  setSelectedStatus: (filter: StatusFilter | null) => void;
}

const DashboardPage: React.FC = () => {
  // Get data from contexts
  const { orders, updateOrder, clearAllOrders: clearOrdersFromContext } = useOrderData();
  const { selectedStatus, setSelectedStatus } = useOutletContext<LayoutOutletContext>();
  
  // Wrapper functions to maintain compatibility
  const onOrdersUpdate = (newOrders: Order[]) => {
    // Update localStorage directly
    localStorage.setItem('orders', JSON.stringify(newOrders));
    // Dispatch custom event to notify OrderDataContext
    window.dispatchEvent(new Event('orders-updated'));
  };
  
  const onStatusSelect = setSelectedStatus;
  
  const clearAllOrders = clearOrdersFromContext;
  const [openImport, setOpenImport] = useState(false);
  const [dateFromInput, setDateFromInput] = useState<string>("");
  const [dateToInput, setDateToInput] = useState<string>("");
  const [dateFrom, setDateFrom] = useState<Date | null>(null);
  const [dateTo, setDateTo] = useState<Date | null>(null);
  const [vendedorFilter, setVendedorFilter] = useState<string>("");
  const [operadorFilter, setOperadorFilter] = useState<string>("");
  const [vendedores, setVendedores] = useState<string[]>([]);
  const [operadores, setOperadores] = useState<string[]>([]);
  const [filteredOrders, setFilteredOrders] = useState<Order[]>([]);

  // Estado para controlar a ordenação
  const [order, setOrder] = useState<'asc' | 'desc'>('desc');
  const [orderBy, setOrderBy] = useState<string>('dataVenda');

  // Obter informações de autenticação usando UnifiedAuthService
  const userInfo = UnifiedAuthService.getUserInfo();
  const authIsAdmin = userInfo?.role === 'admin';
  const authIsSeller = userInfo?.role === 'seller';
  const authIsOperator = userInfo?.role === 'collector';
  const authIsSupervisor = userInfo?.role === 'supervisor';
  const currentUserName = userInfo?.fullName || '';
  const currentUserEmail = userInfo?.email || '';

  const handleImportSuccess = (importedOrders: Order[]) => {
    onOrdersUpdate(importedOrders);
  };

  const handleOpenImport = () => {
    setOpenImport(true);
  };

  const handleCloseImport = () => {
    setOpenImport(false);
  };

  const formatDateForDisplay = (date: Date | null): string => {
    if (!date) return "";
    try {
      const day = String(date.getUTCDate()).padStart(2, '0');
      const month = String(date.getUTCMonth() + 1).padStart(2, '0');
      const year = date.getUTCFullYear();
      return `${day}/${month}/${year}`;
    } catch (e) {
      return "Invalid Date";
    }
  };

  const handleApplyDateFilter = () => {
    const from = dateFromInput ? new Date(dateFromInput + 'T00:00:00Z') : null;
    const to = dateToInput ? new Date(dateToInput + 'T00:00:00Z') : null;

    if (from && isNaN(from.getTime())) {
        setDateFrom(null);
    } else {
        setDateFrom(from);
    }

    if (to && isNaN(to.getTime())) {
        setDateTo(null);
    } else {
        if(to) {
            to.setUTCHours(23, 59, 59, 999);
        }
        setDateTo(to);
    }
  };

  const handleClearDateFilter = () => {
    setDateFromInput("");
    setDateToInput("");
    setDateFrom(null);
    setDateTo(null);
  };

  const handleClearAllFilters = () => {
    handleClearDateFilter();
    setVendedorFilter("");
    setOperadorFilter("");
  };

  // Extrair vendedores e operadores únicos dos pedidos
  useEffect(() => {
    if (!orders || orders.length === 0) return;

    // Extrair vendedores únicos
    const uniqueVendedores = Array.from(new Set(
      orders
        .map(order => order.vendedor)
        .filter(vendedor => vendedor && vendedor.trim() !== '')
    )).sort();

    // Extrair operadores únicos
    const uniqueOperadores = Array.from(new Set(
      orders
        .map(order => order.operador)
        .filter(operador => operador && operador.trim() !== '')
    )).sort();

    setVendedores(uniqueVendedores);
    setOperadores(uniqueOperadores);

  }, [orders]);

  // Effect para filtrar pedidos quando os filtros mudam
  useEffect(() => {
    let filteredResults = [...orders];


    // 1. Filtrar por papel do usuário
    // Admin e supervisor podem ver todos os pedidos
    if (authIsAdmin || authIsSupervisor) {
      // Não aplicar filtro - mostrar todos os pedidos
      // Log para depuração
    }
    // Vendedor só pode ver seus próprios pedidos
    else if (authIsSeller) {
      filteredResults = filteredResults.filter(order => {
        // Verificar se o vendedor está definido
        if (!order.vendedor) return false;

        // Comparar ignorando case e espaços extras
        const vendedorNormalizado = order.vendedor.toLowerCase().trim();
        const userNameNormalizado = currentUserName ? currentUserName.toLowerCase().trim() : '';
        const userEmailNormalizado = currentUserEmail ? currentUserEmail.toLowerCase().trim() : '';

        // Verificar se o nome do vendedor contém o nome do usuário ou vice-versa
        const isMatch = vendedorNormalizado.includes(userNameNormalizado) ||
                       userNameNormalizado.includes(vendedorNormalizado) ||
                       vendedorNormalizado.includes(userEmailNormalizado) ||
                       userEmailNormalizado.includes(vendedorNormalizado);

        // Log para depuração
        if (order.situacaoVenda === 'Liberação') {
        }

        return isMatch;
      });
    }
    // Operador só pode ver pedidos atribuídos a ele
    else if (authIsOperator) {
      filteredResults = filteredResults.filter(order => {
        if (!order.operador) return false;

        const operadorNormalizado = order.operador.toLowerCase().trim();
        const userNameNormalizado = currentUserName ? currentUserName.toLowerCase().trim() : '';
        const userEmailNormalizado = currentUserEmail ? currentUserEmail.toLowerCase().trim() : '';

        return operadorNormalizado.includes(userNameNormalizado) ||
               userNameNormalizado.includes(operadorNormalizado) ||
               operadorNormalizado.includes(userEmailNormalizado) ||
               userEmailNormalizado.includes(operadorNormalizado);
      });
    }

    // 2. Filtrar pedidos deletados
    if (!authIsAdmin && (!selectedStatus || selectedStatus?.value !== 'deletado')) {
      filteredResults = filteredResults.filter(order =>
        !order.situacaoVenda ||
        order.situacaoVenda.toLowerCase() !== 'deletado'
      );
    }

    // 3. Aplicar filtro de status da sidebar
    if (selectedStatus) {

      if (selectedStatus.field === 'situacaoVenda') {
        // Log all order statuses to debug

        // Use case-insensitive comparison
        filteredResults = filteredResults.filter(order => {
          const orderStatus = (order.situacaoVenda || '').toLowerCase();
          const filterValue = selectedStatus.value.toLowerCase();
          const matches = orderStatus === filterValue;

          // Debug specific orders

          return matches;
        });
      } else if (selectedStatus.field === 'special' && selectedStatus.value === 'dataRecebimento') {
        const today = getTodayDateBR();
        filteredResults = filteredResults.filter(order => order.dataRecebimento === today);
      }
    }

    // 4. Aplicar filtros de data
    if (dateFrom || dateTo) {
      filteredResults = filteredResults.filter(order => {
      try {
        const parts = order.dataVenda.split('/');
        if (parts.length !== 3) return false;

        const [day, month, year] = parts.map(Number);
        if (isNaN(day) || isNaN(month) || isNaN(year)) return false;

        const orderDate = new Date(Date.UTC(year, month - 1, day));
        if (isNaN(orderDate.getTime())) return false;

        const afterFrom = dateFrom ? orderDate >= dateFrom : true;
        const beforeTo = dateTo ? orderDate <= dateTo : true;

          return afterFrom && beforeTo;
      } catch (e) {
          return false;
        }
      });
    }

    // 5. Aplicar filtro de vendedor
    if (vendedorFilter) {
      filteredResults = filteredResults.filter(order =>
        order.vendedor && order.vendedor.toLowerCase().includes(vendedorFilter.toLowerCase())
      );
    }

    // 6. Aplicar filtro de operador
    if (operadorFilter) {
      filteredResults = filteredResults.filter(order =>
        order.operador && order.operador.toLowerCase().includes(operadorFilter.toLowerCase())
      );
    }

    // 7. Aplicar ordenação
    filteredResults.sort((a, b) => {
      try {
        if (orderBy === 'dataVenda') {
          const dateA = parseDate(a.dataVenda);
          const dateB = parseDate(b.dataVenda);
          return order === 'desc'
            ? dateB.getTime() - dateA.getTime() // Ordem decrescente (mais recente primeiro)
            : dateA.getTime() - dateB.getTime(); // Ordem crescente (mais antigo primeiro)
        }
        return 0;
      } catch (error) {
        return 0;
      }
    });

    setFilteredOrders(filteredResults);

  }, [
    orders,
    selectedStatus,
    authIsAdmin,
    authIsSeller,
    authIsOperator,
    authIsSupervisor,
    currentUserName,
    currentUserEmail,
    dateFrom,
    dateTo,
    vendedorFilter,
    operadorFilter,
    order,
    orderBy
  ]);

  // Função para obter vendedores únicos, mostrando apenas o seu próprio para vendedores
  const getUniqueVendedores = () => {
    if (authIsSeller) {
      // Se for vendedor, retornar apenas o próprio nome
      return [currentUserName].filter(Boolean);
    }

    // Para admin e supervisor, mostrar todos
    return Array.from(
      new Set(
        filteredOrders
          .map((order) => order.vendedor)
          .filter((vendedor) => vendedor && vendedor.trim() !== '')
      )
    );
  };

  // Função para obter operadores únicos, mostrando apenas o seu próprio para operadores
  const getUniqueOperadores = () => {
    if (authIsOperator) {
      // Se for operador, retornar apenas o próprio nome
      return [currentUserName].filter(Boolean);
    }

    // Para admin e supervisor, mostrar todos
    return Array.from(
      new Set(
        filteredOrders
          .map((order) => order.operador)
          .filter((operador) => operador && operador.trim() !== '')
      )
    );
  };

  // Filtrar pedidos com base no papel do usuário
  const filteredByUserRole = filteredOrders.filter(order => {
    // Admins e supervisores podem ver todos os pedidos
    if (authIsAdmin || authIsSupervisor) {
      // Log para depuração
      if (order.situacaoVenda === 'Liberação') {
      }
      return true;
    }

    // Vendedores só podem ver seus próprios pedidos
    if (authIsSeller && userInfo?.fullName) {
      // Verificar se o vendedor está definido
      if (!order.vendedor) return false;

      // Comparar ignorando case e espaços extras
      const vendedorNormalizado = order.vendedor.toLowerCase().trim();
      const userNameNormalizado = userInfo.fullName.toLowerCase().trim();
      const userEmailNormalizado = userInfo.email ? userInfo.email.toLowerCase().trim() : '';

      // Verificar se o nome do vendedor contém o nome do usuário ou vice-versa
      const isMatch = vendedorNormalizado.includes(userNameNormalizado) ||
                     userNameNormalizado.includes(vendedorNormalizado) ||
                     vendedorNormalizado.includes(userEmailNormalizado) ||
                     userEmailNormalizado.includes(vendedorNormalizado);

      // Log para depuração
      if (order.situacaoVenda === 'Liberação') {
      }

      return isMatch;
    }

    // Operadores só podem ver pedidos atribuídos a eles
    if (authIsOperator && userInfo?.fullName) {
      if (!order.operador) return false;

      const operadorNormalizado = order.operador.toLowerCase().trim();
      const userNameNormalizado = userInfo.fullName.toLowerCase().trim();
      const userEmailNormalizado = userInfo.email ? userInfo.email.toLowerCase().trim() : '';

      return operadorNormalizado.includes(userNameNormalizado) ||
             userNameNormalizado.includes(operadorNormalizado) ||
             operadorNormalizado.includes(userEmailNormalizado) ||
             userEmailNormalizado.includes(operadorNormalizado);
    }

    // Para outros usuários, mostrar todos os pedidos não deletados
    return true;
  });

  // Filtrar pedidos deletados (só mostrar para administradores ou quando explicitamente filtrado)
  const ordersWithoutDeleted = filteredByUserRole.filter(order => {
    // Se o usuário não é admin, ocultar pedidos deletados
    // EXCETO se o status selecionado for explicitamente "deletado"
    const isDeletedOrder = order.situacaoVenda?.toLowerCase() === 'deletado';
    const isExplicitlyFilteringDeleted = selectedStatus?.field === 'situacaoVenda' &&
                                         selectedStatus?.value?.toLowerCase() === 'deletado';

    if (isDeletedOrder && !authIsAdmin && !isExplicitlyFilteringDeleted) {
      return false;
    }

    return true;
  });

  // Usar ordersWithoutDeleted em vez de finalFilteredOrders para cálculos e exibição

  // Função auxiliar para converter data BR (DD/MM/YYYY) para objeto Date
  const parseDate = (dateStr: string): Date => {
    if (!dateStr) return new Date(0); // Data mínima para valores vazios

    try {
      const parts = dateStr.split('/');
      // Se não tiver formato DD/MM/YYYY, retornar como string
      if (parts.length !== 3) return new Date(dateStr);

      const [day, month, year] = parts.map(Number);
      return new Date(year, month - 1, day);
    } catch (e) {
      return new Date(0);
    }
  };

  // Get conversion settings
  const { conversionSettings, applyConversion } = useConversion();

  // Calculate metrics from orders
  const rawTotalSales = ordersWithoutDeleted.reduce((sum, order) => sum + order.valorVenda, 0);

  // For completed sales with empty valor recebido, use the valor venda as the valor recebido
  const rawTotalReceived = ordersWithoutDeleted.reduce((sum, order) => {
    // Check if the order is complete and has empty valor recebido
    if (
      typeof order.situacaoVenda === 'string' &&
      order.situacaoVenda.toLowerCase() === 'completo' &&
      (!order.valorRecebido || order.valorRecebido === 0)
    ) {
      // Use the valor venda as the valor recebido
      return sum + order.valorVenda;
    } else {
      // Otherwise use the actual valor recebido
      return sum + order.valorRecebido;
    }
  }, 0);

  const rawCompletedOrders = ordersWithoutDeleted.filter(o =>
    typeof o.situacaoVenda === 'string' &&
    o.situacaoVenda.toLowerCase() === 'completo'
  ).length;

  // Apply conversion rates if enabled, otherwise use the calculated values
  const totalSales = conversionSettings.enabled ? rawTotalSales : rawTotalSales;

  // For received value, use either the converted value or the calculated value
  const totalReceived = conversionSettings.enabled
    ? applyConversion(totalSales, 'revenueConversion') // When enabled, received value is a percentage of total sales
    : rawTotalReceived; // When disabled, use the calculated value

  const completedOrders = rawCompletedOrders; // Keep the actual count of completed orders
  const filteredOrdersCount = filteredOrders.length; // Keep the actual count of filtered orders

  // Calculate the actual conversion rate based on completed orders
  const actualConversionRate = Math.round((rawCompletedOrders / Math.max(filteredOrdersCount, 1)) * 100);

  // For conversion rate, use either the setting value or the calculated conversion rate
  const conversionRate = conversionSettings.enabled
    ? conversionSettings.revenueConversion
    : actualConversionRate;

  const getTodayDateBR = (): string => {
    // Criar data usando timezone de São Paulo, Brasil (GMT-3)
    const today = new Date();
    const offset = -3; // São Paulo, Brasil (GMT-3)
    const localTime = new Date(today.getTime() + offset * 3600 * 1000);

    const day = String(localTime.getUTCDate()).padStart(2, '0');
    const month = String(localTime.getUTCMonth() + 1).padStart(2, '0');
    const year = localTime.getUTCFullYear();

    return `${day}/${month}/${year}`;
  };

  const todayDate = getTodayDateBR();

  // Calcula recebimentos do dia atual
  const todayFullPayments = filteredOrders
    .filter(order => order.dataRecebimento === todayDate)
    .reduce((sum, order) => {
      // Check if the order is complete and has empty valor recebido
      if (
        typeof order.situacaoVenda === 'string' &&
        order.situacaoVenda.toLowerCase() === 'completo' &&
        (!order.valorRecebido || order.valorRecebido === 0)
      ) {
        // Use the valor venda as the valor recebido
        return sum + order.valorVenda;
      } else {
        // Otherwise use the actual valor recebido
        return sum + order.valorRecebido;
      }
    }, 0);

  const todayPartialPayments = filteredOrders
    .filter(order => order.dataPagamentoParcial === todayDate)
    .reduce((sum, order) => sum + order.pagamentoParcial, 0);

  // Função para atualizar um pedido na lista
  const handleOrderUpdate = async (updatedOrder: Order) => {
    try {
      await updateOrder(updatedOrder);
    } catch (error) {
    }
  };

  // Preparar dados para a tabela de resumo
  const statusCounts = filteredOrders.reduce((acc: Record<string, number>, order) => {
    const status = order.situacaoVenda || 'Desconhecido';
    acc[status] = (acc[status] || 0) + 1;
    return acc;
  }, {});

  // Converter para array para exibir
  const statusCountsArray = Object.entries(statusCounts)
    .map(([status, count]) => ({ status, count }))
    .sort((a, b) => b.count - a.count);

  // Listen for custom filter update events
  useEffect(() => {
    const handleUpdateFilter = (event: CustomEvent) => {
      // Force refiltering by setting a dummy state that will trigger a re-render
      setFilteredOrders([]); // Clear the filtered orders to force a refresh
    };

    window.addEventListener('update-dashboard-filter', handleUpdateFilter as EventListener);

    return () => {
      window.removeEventListener('update-dashboard-filter', handleUpdateFilter as EventListener);
    };
  }, []);

  return (
    <Box sx={{ width: '100%', maxWidth: '100%', overflow: 'hidden', p: 2.5 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2.5, flexWrap: 'wrap', gap: 1.8 }}>
        <Box sx={{ flexGrow: 1 }}>
          <Typography variant="h4" sx={{ 
            fontWeight: 800,
            fontSize: '1.8rem',
            background: 'linear-gradient(135deg, #1a1a1a 0%, #424242 100%)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            mb: 0.4
          }}>
            Home Dashboard
          </Typography>
          <Typography variant="body1" sx={{ 
            color: 'text.secondary',
            fontSize: '0.875rem'
          }}>
            Visão geral de todos os pedidos e desempenho em tempo real
          </Typography>
        </Box>

        <Box sx={{
          display: 'flex',
          flexWrap: 'wrap',
          gap: 1.8,
          mb: 2.5,
        }}>
          {/* Filtros em Cards Separados */}

          {/* Card de Filtro por Data */}
          <Paper
            elevation={0}
            sx={{
              p: 1.8,
              flex: '1 1 270px',
              borderRadius: '9px',
              border: '1px solid rgba(0, 0, 0, 0.05)',
              boxShadow: '0 1px 3px rgba(0,0,0,0.02)',
              display: 'flex',
              flexDirection: 'column',
              gap: 1.8
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.9, mb: 0.9 }}>
              <CalendarTodayIcon sx={{ fontSize: '1.1rem' }} color="primary" />
              <Typography variant="subtitle2" fontWeight={600} sx={{ fontSize: '0.8125rem' }}>Período</Typography>
            </Box>

            <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={ptBR}>
              <Box sx={{ display: 'flex', gap: 2 }}>
                <DatePicker
                  label="Data Inicial"
                  value={dateFromInput ? new Date(dateFromInput) : null}
                  onChange={(newValue) => setDateFromInput(newValue ? newValue.toISOString().split('T')[0] : '')}
                  format="dd/MM/yyyy"
                  slotProps={{
                    textField: {
                      size: "small",
                      fullWidth: true,
                      sx: { bgcolor: 'white' }
                    }
                  }}
                />
                <DatePicker
                  label="Data Final"
                  value={dateToInput ? new Date(dateToInput) : null}
                  onChange={(newValue) => setDateToInput(newValue ? newValue.toISOString().split('T')[0] : '')}
                  format="dd/MM/yyyy"
                  slotProps={{
                    textField: {
                      size: "small",
                      fullWidth: true,
                      sx: { bgcolor: 'white' }
                    }
                  }}
                />
              </Box>
            </LocalizationProvider>
          </Paper>

          {/* Card de Filtro por Vendedor */}
          <Paper
            elevation={0}
            sx={{
              p: 1.8,
              flex: '1 1 180px',
              borderRadius: '9px',
              border: '1px solid rgba(0, 0, 0, 0.05)',
              boxShadow: '0 1px 3px rgba(0,0,0,0.02)',
              display: 'flex',
              flexDirection: 'column',
              gap: 1.8
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.9, mb: 0.9 }}>
              <PersonAddIcon sx={{ fontSize: '1.1rem' }} color="success" />
              <Typography variant="subtitle2" fontWeight={600} sx={{ fontSize: '0.8125rem' }}>Vendedor</Typography>
            </Box>

            <FormControl fullWidth size="small" sx={{ bgcolor: 'white', borderRadius: '7px' }}>
              <Select
                value={vendedorFilter}
                onChange={(e: SelectChangeEvent) => setVendedorFilter(e.target.value)}
                displayEmpty
                sx={{ borderRadius: '7px', fontSize: '0.8125rem' }}
                renderValue={(selected) => {
                  if (selected === "") {
                    return <span style={{ color: 'rgba(0, 0, 0, 0.6)' }}>Todos os vendedores</span>;
                  }
                  return selected;
                }}
                endAdornment={vendedorFilter ? (
                  <IconButton
                    size="small"
                    onClick={(e) => {
                      e.stopPropagation();
                      setVendedorFilter('');
                    }}
                    sx={{ mr: 0.5 }}
                  >
                    <CloseIcon fontSize="small" />
                  </IconButton>
                ) : null}
              >
                <MenuItem value="">
                  <em>Todos os vendedores</em>
                </MenuItem>
                {getUniqueVendedores().map((vendedor) => (
                  <MenuItem key={vendedor} value={vendedor}>
                    {vendedor}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Paper>

          {/* Card de Filtro por Operador */}
          <Paper
            elevation={0}
            sx={{
              p: 1.8,
              flex: '1 1 180px',
              borderRadius: '9px',
              border: '1px solid rgba(0, 0, 0, 0.05)',
              boxShadow: '0 1px 3px rgba(0,0,0,0.02)',
              display: 'flex',
              flexDirection: 'column',
              gap: 1.8
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.9, mb: 0.9 }}>
              <PeopleIcon sx={{ fontSize: '1.1rem' }} color="info" />
              <Typography variant="subtitle2" fontWeight={600} sx={{ fontSize: '0.8125rem' }}>Operador</Typography>
            </Box>

            <FormControl fullWidth size="small" sx={{ bgcolor: 'white', borderRadius: '7px' }}>
              <Select
                value={operadorFilter}
                onChange={(e: SelectChangeEvent) => setOperadorFilter(e.target.value)}
                displayEmpty
                sx={{ borderRadius: '7px', fontSize: '0.8125rem' }}
                renderValue={(selected) => {
                  if (selected === "") {
                    return <span style={{ color: 'rgba(0, 0, 0, 0.6)' }}>Todos os operadores</span>;
                  }
                  return selected;
                }}
                endAdornment={operadorFilter ? (
                  <IconButton
                    size="small"
                    onClick={(e) => {
                      e.stopPropagation();
                      setOperadorFilter('');
                    }}
                    sx={{ mr: 0.5 }}
                  >
                    <CloseIcon fontSize="small" />
                  </IconButton>
                ) : null}
              >
                <MenuItem value="">
                  <em>Todos os operadores</em>
                </MenuItem>
                {getUniqueOperadores().map((operador) => (
                  <MenuItem key={operador} value={operador}>
                    {operador}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Paper>

          {/* Card de Ações */}
          <Paper
            elevation={0}
            sx={{
              p: 1.8,
              flex: '0 1 auto',
              borderRadius: '9px',
              border: '1px solid rgba(0, 0, 0, 0.05)',
              boxShadow: '0 1px 3px rgba(0,0,0,0.02)',
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'space-between'
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.9, mb: 0.9 }}>
              <FilterListIcon sx={{ fontSize: '1.1rem' }} color="primary" />
              <Typography variant="subtitle2" fontWeight={600} sx={{ fontSize: '0.8125rem' }}>Ações</Typography>
            </Box>

            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.9, mt: 'auto' }}>
              <Button
                variant="contained"
                size="small"
                startIcon={<RefreshIcon sx={{ fontSize: '1rem' }} />}
                onClick={handleApplyDateFilter}
                sx={{ borderRadius: '7px', fontSize: '0.75rem', py: 0.5 }}
              >
                Aplicar
              </Button>
              <Button
                variant="outlined"
                size="small"
                onClick={handleClearAllFilters}
                sx={{ borderRadius: '7px', fontSize: '0.75rem', py: 0.5 }}
              >
                Limpar
              </Button>
            </Box>
          </Paper>
        </Box>


      </Box>

      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1.8 }}>
        <Typography variant="h6" sx={{ fontWeight: 500, fontSize: '1.08rem' }}>Visão Geral</Typography>
        {conversionSettings.enabled && (
          <Tooltip title="Os valores exibidos estão ajustados de acordo com as configurações de conversão">
            <Chip
              label="Conversão Ativada"
              color="primary"
              variant="outlined"
              size="small"
              sx={{ fontWeight: 'bold', fontSize: '0.7rem', height: '22px' }}
            />
          </Tooltip>
        )}
      </Box>

      <Grid container spacing={2.5} sx={{ mb: 3.6 }}>
        <Grid item xs={12} sm={6} md={3}>
          <OverviewCard
            title="Total de Vendas"
            value={filteredOrdersCount}
            subtitle={dateFrom && dateTo
              ? `${formatDateForDisplay(dateFrom)} - ${formatDateForDisplay(dateTo)}`
              : dateFrom
                ? `A partir de ${formatDateForDisplay(dateFrom)}`
                : dateTo
                  ? `Até ${formatDateForDisplay(dateTo)}`
                  : "Todos os períodos"
            }
            icon={<ShoppingCartIcon />}
            color="primary"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <OverviewCard
            title="Valor Total"
            value={`R$ ${totalSales.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`}
            subtitle={conversionSettings.enabled && conversionSettings.revenueConversion !== 100
              ? `Conversão: ${conversionSettings.revenueConversion}%`
              : `R$ ${(totalSales / Math.max(filteredOrdersCount, 1)).toLocaleString('pt-BR', { minimumFractionDigits: 2 })} por pedido`
            }
            icon={<AttachMoneyIcon />}
            color="success"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <OverviewCard
            title="Valor Recebido"
            value={`R$ ${totalReceived.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`}
            subtitle={conversionSettings.enabled && conversionSettings.revenueConversion !== 100
              ? `Conversão: ${conversionSettings.revenueConversion}%`
              : `${Math.round((totalReceived / totalSales) * 100)}% do total de vendas`
            }
            icon={<AttachMoneyIcon />}
            color="info"
            customValueStyle={{ color: '#4CAF50', fontWeight: 700 }}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <OverviewCard
            title="Taxa de Conversão"
            value={`${conversionRate}%`}
            subtitle={conversionSettings.enabled && conversionSettings.salesConversion !== 100
              ? `Conversão: ${conversionSettings.salesConversion}%`
              : `${completedOrders} pedidos completos`
            }
            icon={<TrendingUpIcon />}
            color="warning"
            showProgressBar={true}
            progressValue={conversionRate}
          />
        </Grid>
      </Grid>

      {/* Nova seção: Recebimentos Hoje */}
      <Paper sx={{ 
        p: 2.5, 
        mb: 3.6, 
        borderRadius: '14px', 
        boxShadow: '0 2px 8px rgba(0,0,0,0.08)', 
        background: '#FFFFFF',
        border: 'none',
        transition: 'all 0.3s ease',
        '&:hover': {
          boxShadow: '0 4px 16px rgba(0,0,0,0.1)'
        }
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2.5 }}>
          <Typography variant="h6" sx={{ flexGrow: 1, fontWeight: 500, fontSize: '1.1rem' }}>
            Recebimentos Hoje
              </Typography>
          <Typography
            variant="body2"
              sx={{
              color: 'text.secondary',
              px: 1.2,
              py: 0.4,
              borderRadius: '7px',
              bgcolor: 'rgba(0, 0, 0, 0.03)',
              fontWeight: 500,
              fontSize: '0.75rem',
            }}
          >
            {todayDate}
          </Typography>
        </Box>

        <Grid container spacing={2}>
          <Grid item xs={12} sm={6}>
            <Card sx={{ p: 1.8, height: '100%', borderRadius: '9px', boxShadow: '0 1px 3px rgba(0,0,0,0.05)', border: '1px solid rgba(0,0,0,0.05)', borderLeft: '3px solid #4CAF50' }}>
              <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                <Typography variant="subtitle2" color="text.secondary" sx={{ fontSize: '0.75rem' }}>
                  Recebimento Total
                </Typography>
                <Typography variant="h4" sx={{ color: '#4CAF50', fontWeight: 700, my: 0.8, fontSize: '1.6rem' }}>
                  R$ {todayFullPayments.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
              </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.75rem' }}>
                  Pagamentos completos recebidos hoje
              </Typography>
              </Box>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6}>
            <Card sx={{ p: 1.8, height: '100%', borderRadius: '9px', boxShadow: '0 1px 3px rgba(0,0,0,0.05)', border: '1px solid rgba(0,0,0,0.05)', borderLeft: '3px solid #2196F3' }}>
              <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                <Typography variant="subtitle2" color="text.secondary" sx={{ fontSize: '0.75rem' }}>
                  Recebimento Parcial
                </Typography>
                <Typography variant="h4" sx={{ color: '#2196F3', fontWeight: 700, my: 0.8, fontSize: '1.6rem' }}>
                  R$ {todayPartialPayments.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
              </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.75rem' }}>
                  Pagamentos parciais recebidos hoje
              </Typography>
              </Box>
            </Card>
          </Grid>
        </Grid>
      </Paper>

      <Paper sx={{ 
        p: 2.5, 
        borderRadius: '14px', 
        boxShadow: '0 2px 8px rgba(0,0,0,0.08)', 
        background: '#FFFFFF',
        border: 'none',
        transition: 'all 0.3s ease',
        '&:hover': {
          boxShadow: '0 4px 16px rgba(0,0,0,0.1)'
        }
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2.5 }}>
          <Typography variant="h5" sx={{ flexGrow: 1, fontWeight: 600, color: '#1a1a1a', fontSize: '1.2rem' }}>
            Pedidos
          </Typography>
          <Box sx={{ display: 'flex', gap: 0.9, alignItems: 'center' }}>
            {vendedorFilter && (
              <Chip
                label={`Vendedor: ${vendedorFilter}`}
                size="small"
                color="primary"
                onDelete={() => setVendedorFilter('')}
                sx={{ borderRadius: '6px', fontSize: '0.7rem', height: '22px' }}
              />
            )}
            {operadorFilter && (
              <Chip
                label={`Operador: ${operadorFilter}`}
                size="small"
                color="info"
                onDelete={() => setOperadorFilter('')}
                sx={{ borderRadius: '6px', fontSize: '0.7rem', height: '22px' }}
              />
            )}
            <Typography
              variant="body2"
              sx={{
                color: 'text.secondary',
                bgcolor: 'rgba(0, 0, 0, 0.03)',
                px: 1.2,
                py: 0.4,
                borderRadius: '7px',
                fontWeight: 500,
                fontSize: '0.75rem',
              }}
            >
              {conversionSettings.enabled ? filteredOrdersCount : filteredOrders.length} pedidos
              {conversionSettings.enabled && conversionSettings.salesConversion !== 100 && (
                <Typography component="span" variant="caption" sx={{ ml: 0.9, color: 'primary.main', fontSize: '0.7rem' }}>
                  (Conversão: {conversionSettings.salesConversion}%)
                </Typography>
              )}
            </Typography>
          </Box>
        </Box>

        <OrdersTableWithPagination orders={ordersWithoutDeleted} onOrderUpdate={handleOrderUpdate} />
      </Paper>

      {/* Tabela de resumo de status - para debug */}
      {process.env.NODE_ENV !== 'production' && (
        <Paper sx={{ p: 3, mb: 3, boxShadow: '0 2px 10px rgba(0,0,0,0.08)' }}>
          <Typography variant="h6" sx={{ mb: 2 }}>Status Summary (Debug)</Typography>
          <TableContainer>
            <Table size="small">
              <TableHead>
                <TableRow>
                  <TableCell>Status</TableCell>
                  <TableCell align="right">Count</TableCell>
                  <TableCell align="right">Filtered Count</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {statusCountsArray.map(({ status, count }) => {
                  // Contar quantos pedidos deste status estão na lista filtrada
                  const filteredCount = filteredOrders.filter(
                    order => order.situacaoVenda === status
                  ).length;

                  return (
                    <TableRow key={status} hover>
                      <TableCell>
                        {status}
                        {selectedStatus &&
                         selectedStatus.field === 'situacaoVenda' &&
                         selectedStatus.value.toLowerCase() === status.toLowerCase() &&
                         ' (Selected)'}
                      </TableCell>
                      <TableCell align="right">{count}</TableCell>
                      <TableCell align="right">{filteredCount}</TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </TableContainer>
        </Paper>
      )}

      <ImportDialog
        open={openImport}
        onClose={handleCloseImport}
        onImportSuccess={handleImportSuccess}
      />

    </Box>
  );
};

export default DashboardPage;