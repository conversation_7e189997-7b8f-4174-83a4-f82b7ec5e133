import React, { useState, useEffect } from 'react';
import {
  Container,
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Button,
  IconButton,
  Chip,
  Stack,
  Avatar,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Fab,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Skeleton,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Tooltip,
  Switch,
  FormControlLabel,
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  MoreVert as MoreVertIcon,
  Image as ImageIcon,
  AttachMoney as AttachMoneyIcon,
  LocalOffer as LocalOfferIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  ContentCopy as ContentCopyIcon,
  Archive as ArchiveIcon,
  Unarchive as UnarchiveIcon,
  FilterList as FilterListIcon,
} from '@mui/icons-material';
import { useParams, useNavigate } from 'react-router-dom';
import { getProduct, productService } from '../services/ProductService';
import { kitService } from '../services/KitService';
import KitCreationDialog from '../components/KitCreationDialog';
import { VariationType, Product, ProductVariation, Kit } from '../types/Product';
import { KitEditDialog } from '../components/KitEditDialog';

const variationTypeLabels: Record<VariationType, string> = {
  [VariationType.CAPSULAS]: 'Cápsulas',
  [VariationType.GOTAS]: 'Gotas',
  [VariationType.GEL]: 'Gel',
  [VariationType.SPRAY]: 'Spray',
  [VariationType.CREME]: 'Creme',
  [VariationType.CUSTOM]: 'Personalizado',
};

// Types are imported from '../types/Product'

const ProductDetailV2Page: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [product, setProduct] = useState<Product | null>(null);
  const [kits, setKits] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [kitDialogOpen, setKitDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [kitToDelete, setKitToDelete] = useState<any | null>(null);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedKit, setSelectedKit] = useState<any | null>(null);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [kitFilter, setKitFilter] = useState<'all' | 'active' | 'inactive'>('all');
  const [filterAnchorEl, setFilterAnchorEl] = useState<null | HTMLElement>(null);

  useEffect(() => {
    if (id) {
      fetchProduct();
      fetchKits();
    }
  }, [id]);

  const fetchProduct = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await getProduct(id!);
      setProduct(data);
    } catch (err) {
      console.error('Error fetching product:', err);
      setError('Erro ao carregar produto');
    } finally {
      setLoading(false);
    }
  };

  const fetchKits = async () => {
    try {
      const data = await kitService.getKitsByProductId(id!);
      setKits(data);
    } catch (err) {
      console.error('Error fetching kits:', err);
    }
  };

  const handleCreateKit = async (kitData: any) => {
    try {
      console.log('Creating kit with data:', kitData);
      await productService.createKit(id!, kitData);
      // Refresh kits list
      await fetchKits();
      setKitDialogOpen(false); // Close dialog on success
      alert('Kit criado com sucesso!');
    } catch (err: any) {
      console.error('Error creating kit:', err);
      // Show error to user
      const errorMessage = err.response?.data?.message || err.message || 'Erro ao criar kit';
      alert(`Erro ao criar kit: ${errorMessage}`);
    }
  };

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, kit: any) => {
    setAnchorEl(event.currentTarget);
    setSelectedKit(kit);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedKit(null);
  };

  const handleEditKit = (kit: any) => {
    setSelectedKit(kit);
    setEditDialogOpen(true);
    handleMenuClose();
  };

  const handleSaveKit = async (updatedKit: any) => {
    try {
      await kitService.updateKit(updatedKit.id, {
        name: updatedKit.name,
        active: updatedKit.active
      });
      await fetchKits();
      setEditDialogOpen(false);
    } catch (err) {
      console.error('Error updating kit:', err);
      setError('Erro ao atualizar kit');
    }
  };

  const handleFilterMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setFilterAnchorEl(event.currentTarget);
  };

  const handleFilterMenuClose = () => {
    setFilterAnchorEl(null);
  };

  const handleFilterChange = (filter: 'all' | 'active' | 'inactive') => {
    setKitFilter(filter);
    handleFilterMenuClose();
  };

  const getFilteredKits = () => {
    switch (kitFilter) {
      case 'active':
        return kits.filter(kit => kit.active);
      case 'inactive':
        return kits.filter(kit => !kit.active);
      default:
        return kits;
    }
  };

  const handleToggleKitActive = async (kit: any) => {
    try {
      await kitService.updateKit(kit.id, { active: !kit.active });
      await fetchKits();
      handleMenuClose();
    } catch (err) {
      console.error('Error updating kit:', err);
      setError('Erro ao atualizar kit');
    }
  };

  const handleDeleteKitClick = (kit: any) => {
    setKitToDelete(kit);
    setDeleteDialogOpen(true);
    handleMenuClose();
  };

  const handleDeleteConfirm = async () => {
    if (!kitToDelete) return;

    try {
      await kitService.deleteKit(kitToDelete.id);
      await fetchKits();
      setDeleteDialogOpen(false);
      setKitToDelete(null);
    } catch (err) {
      console.error('Error deleting kit:', err);
      setError('Erro ao deletar kit');
    }
  };

  const getKitComposition = (kit: any) => {
    if (!kit.items) return '';
    return kit.items.map((item: any) => {
      const variation = item.productVariation;
      if (!variation) return '';
      return `${item.quantity} ${variation.variation || ''}`;
    }).join(', ');
  };

  const calculateKitCost = (kit: any) => {
    if (!kit.items) return 0;
    return kit.items.reduce((sum: number, item: any) => {
      const price = item.productVariation?.price || 0;
      return sum + (price * item.quantity);
    }, 0);
  };

  const calculateKitMargin = (kit: any) => {
    const cost = calculateKitCost(kit);
    const price = kit.price || 0;
    if (price === 0) return 0;
    return ((price - cost) / price * 100);
  };

  if (loading) {
    return (
      <Container maxWidth="xl">
        <Box sx={{ mb: 4 }}>
          <Skeleton variant="text" width={200} height={40} />
          <Skeleton variant="text" width={300} />
        </Box>
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Skeleton variant="rectangular" height={200} />
          </Grid>
        </Grid>
      </Container>
    );
  }

  if (error || !product) {
    return (
      <Container maxWidth="xl">
        <Alert severity="error">
          {error || 'Produto não encontrado'}
        </Alert>
        <Button
          startIcon={<ArrowBackIcon />}
          onClick={() => navigate('/dashboard/produtos')}
          sx={{ mt: 2 }}
        >
          Voltar para Produtos
        </Button>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl">
      {/* Header */}
      <Box sx={{ mb: 4, display: 'flex', alignItems: 'center', gap: 2 }}>
        <IconButton onClick={() => navigate('/dashboard/produtos')}>
          <ArrowBackIcon />
        </IconButton>
        <Box sx={{ flexGrow: 1 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Avatar
              src={product.imageUrl}
              sx={{ width: 60, height: 60 }}
            >
              <ImageIcon />
            </Avatar>
            <Box>
              <Typography variant="h4" component="h1" gutterBottom>
                {product.name}
              </Typography>
              <Typography variant="body1" color="text.secondary">
                {product.description || 'Sem descrição'}
              </Typography>
            </Box>
          </Box>
        </Box>
        <Chip
          label={product.active ? 'Ativo' : 'Inativo'}
          color={product.active ? 'success' : 'default'}
          icon={product.active ? <CheckCircleIcon /> : <CancelIcon />}
        />
      </Box>

      <Grid container spacing={3}>
        {/* Variations Section */}
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Variações do Produto
            </Typography>
            <Stack spacing={2}>
              {(product.variations ?? []).map((variation) => (
                <Card key={variation.id} variant="outlined">
                  <CardContent>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Box>
                        <Typography variant="subtitle1">
                          {variation.type === VariationType.CUSTOM 
                            ? variation.customName 
                            : variationTypeLabels[variation.type]}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Custo: R$ {variation.price.toFixed(2)}
                        </Typography>
                      </Box>
                      <Chip 
                        label={variation.type} 
                        size="small" 
                        variant="outlined"
                      />
                    </Box>
                  </CardContent>
                </Card>
              ))}
            </Stack>
          </Paper>
        </Grid>

        {/* Kits Section */}
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Typography variant="h6">
                  Kits ({getFilteredKits().length} de {kits.length})
                </Typography>
                <Button
                  size="small"
                  startIcon={<FilterListIcon />}
                  onClick={handleFilterMenuOpen}
                  variant="outlined"
                >
                  {kitFilter === 'all' ? 'Todos' : kitFilter === 'active' ? 'Ativos' : 'Inativos'}
                </Button>
              </Box>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => setKitDialogOpen(true)}
              >
                Criar Kit
              </Button>
            </Box>

            {kits.length === 0 ? (
              <Box sx={{ textAlign: 'center', py: 4 }}>
                <LocalOfferIcon sx={{ fontSize: 60, color: 'text.secondary', mb: 2 }} />
                <Typography variant="h6" color="text.secondary" gutterBottom>
                  Nenhum kit criado
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Crie kits combinando as variações do produto
                </Typography>
              </Box>
            ) : (
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Kit</TableCell>
                      <TableCell>Composição</TableCell>
                      <TableCell align="right">Custo</TableCell>
                      <TableCell align="right">Preço</TableCell>
                      <TableCell align="center">Margem</TableCell>
                      <TableCell align="center">Status</TableCell>
                      <TableCell align="center">Vendas</TableCell>
                      <TableCell align="right">Ações</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {getFilteredKits().map((kit: any) => {
                      const cost = calculateKitCost(kit);
                      const margin = calculateKitMargin(kit);
                      
                      return (
                        <TableRow key={kit.id}>
                          <TableCell>
                            <Box>
                              <Typography variant="subtitle2">{kit.name}</Typography>
                              <Typography variant="caption" color="text.secondary">
                                Kit ID: {kit.id.substring(0, 8)}
                              </Typography>
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">
                              {getKitComposition(kit)}
                            </Typography>
                          </TableCell>
                          <TableCell align="right">
                            <Typography variant="body2" color="text.secondary">
                              R$ {cost.toFixed(2)}
                            </Typography>
                          </TableCell>
                          <TableCell align="right">
                            <Typography variant="body2" fontWeight="bold">
                              R$ {(kit.price || 0).toFixed(2)}
                            </Typography>
                          </TableCell>
                          <TableCell align="center">
                            <Chip
                              label={`${margin.toFixed(1)}%`}
                              size="small"
                              color={margin > 50 ? 'success' : margin > 30 ? 'warning' : 'error'}
                            />
                          </TableCell>
                          <TableCell align="center">
                            <Chip
                              label={kit.active ? 'Ativo' : 'Inativo'}
                              color={kit.active ? 'success' : 'default'}
                              size="small"
                            />
                          </TableCell>
                          <TableCell align="center">
                            <Typography variant="body2">
                              {kit._count?.orders || 0}
                            </Typography>
                          </TableCell>
                          <TableCell align="right">
                            <IconButton
                              size="small"
                              onClick={(e) => handleMenuOpen(e, kit)}
                            >
                              <MoreVertIcon />
                            </IconButton>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </TableContainer>
            )}
            
          </Paper>
        </Grid>
      </Grid>

      {/* Kit Creation Dialog */}
      {product && (
        <KitCreationDialog
          open={kitDialogOpen}
          onClose={() => setKitDialogOpen(false)}
          onSave={handleCreateKit}
          productName={product.name}
          variations={product.variations ?? []}
        />
      )}

      {/* Kit Actions Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={() => handleEditKit(selectedKit!)}>
          <ListItemIcon>
            <EditIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Editar</ListItemText>
        </MenuItem>
        <MenuItem onClick={() => handleToggleKitActive(selectedKit!)}>
          <ListItemIcon>
            {selectedKit?.active ? <ArchiveIcon fontSize="small" /> : <UnarchiveIcon fontSize="small" />}
          </ListItemIcon>
          <ListItemText>{selectedKit?.active ? 'Desativar' : 'Ativar'}</ListItemText>
        </MenuItem>
        <Divider />
        <MenuItem onClick={() => handleDeleteKitClick(selectedKit!)}>
          <ListItemIcon>
            <DeleteIcon fontSize="small" color="error" />
          </ListItemIcon>
          <ListItemText>Excluir</ListItemText>
        </MenuItem>
      </Menu>

      {/* Filter Menu */}
      <Menu
        anchorEl={filterAnchorEl}
        open={Boolean(filterAnchorEl)}
        onClose={handleFilterMenuClose}
      >
        <MenuItem onClick={() => handleFilterChange('all')} selected={kitFilter === 'all'}>
          <ListItemText>Todos os Kits</ListItemText>
        </MenuItem>
        <MenuItem onClick={() => handleFilterChange('active')} selected={kitFilter === 'active'}>
          <ListItemText>Apenas Ativos</ListItemText>
        </MenuItem>
        <MenuItem onClick={() => handleFilterChange('inactive')} selected={kitFilter === 'inactive'}>
          <ListItemText>Apenas Inativos</ListItemText>
        </MenuItem>
      </Menu>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>Confirmar Exclusão</DialogTitle>
        <DialogContent>
          <Typography>
            Tem certeza que deseja excluir o kit "{kitToDelete?.name}"?
          </Typography>
          {kitToDelete?._count?.orders && kitToDelete._count.orders > 0 && (
            <Alert severity="warning" sx={{ mt: 2 }}>
              Este kit possui {kitToDelete._count.orders} vendas e será desativado ao invés de excluído.
            </Alert>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancelar</Button>
          <Button onClick={handleDeleteConfirm} color="error" variant="contained">
            Excluir
          </Button>
        </DialogActions>
      </Dialog>

      {/* Kit Edit Dialog */}
      {selectedKit && (
        <KitEditDialog
          kit={selectedKit}
          open={editDialogOpen}
          onOpenChange={setEditDialogOpen}
          onSave={handleSaveKit}
        />
      )}
    </Container>
  );
};

export default ProductDetailV2Page;