import React, { useState, useEffect } from 'react';
import {
  Con<PERSON>er,
  Box,
  Typography,
  Button,
  Paper,
  TextField,
  Alert,
  Divider,
  Stack,
  Chip,
  Card,
  CardContent,
  Grid,
  IconButton,
  Collapse,
} from '@mui/material';
import {
  ContentCopy as CopyIcon,
  ExpandMore as ExpandMoreIcon,
  CheckCircle as CheckIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
} from '@mui/icons-material';
import { productService } from '../services/ProductService';
import { transformProductForBackend } from '../utils/productTransformers';

const ProductCreationDebugPage: React.FC = () => {
  const [testResults, setTestResults] = useState<any[]>([]);
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({});

  useEffect(() => {
    // Run tests on component mount
    runAllTests();
  }, []);

  const toggleSection = (section: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const runAllTests = async () => {
    const results: any[] = [];

    // Test 1: Valid minimal payload
    results.push(await testPayload({
      name: 'Test Product Minimal',
      variations: [{
        variation: 'Cápsulas',
        sku: 'TEST-MIN-001',
        price: 50
      }]
    }, 'Minimal Valid Payload'));

    // Test 2: Full valid payload
    results.push(await testPayload({
      name: 'Test Product Full',
      description: 'A complete test product',
      price: 100,
      imageUrl: 'https://example.com/image.jpg',
      variations: [
        {
          variation: 'Cápsulas',
          sku: 'TEST-FULL-001',
          price: 50
        },
        {
          variation: 'Gotas',
          sku: 'TEST-FULL-002',
          price: 75
        }
      ]
    }, 'Full Valid Payload'));

    // Test 3: Payload with extra fields (should fail)
    results.push(await testPayload({
      name: 'Test Product Extra Fields',
      tenantId: 'tenant-123', // Extra field
      type: 'PRODUCT', // Extra field
      kits: [], // Extra field
      variations: [{
        variation: 'Cápsulas',
        sku: 'TEST-EXTRA-001',
        price: 50,
        costPrice: 25, // Extra field
        active: true, // Extra field
        type: 'CAPSULAS', // Extra field
      }]
    }, 'Payload with Extra Fields'));

    // Test 4: Test transformer function
    results.push(await testTransformer());

    // Test 5: Invalid payload - missing variations
    results.push(await testPayload({
      name: 'Test Product No Variations',
      variations: []
    }, 'Invalid - No Variations'));

    // Test 6: Invalid payload - missing required fields in variation
    results.push(await testPayload({
      name: 'Test Product Invalid Variation',
      variations: [{
        variation: 'Cápsulas',
        // Missing sku and price
      }]
    }, 'Invalid - Missing Variation Fields'));

    setTestResults(results);
  };

  const testPayload = async (payload: any, testName: string) => {
    console.log(`🧪 Running test: ${testName}`);
    console.log('Payload:', JSON.stringify(payload, null, 2));

    const result: any = {
      testName,
      payload,
      sentToBackend: null,
      response: null,
      error: null,
      status: 'pending'
    };

    try {
      // Capture what will be sent
      const transformedPayload = transformProductForBackend(payload);
      result.sentToBackend = transformedPayload;

      // Make the actual API call
      const response = await productService.createProduct(payload);
      result.response = response;
      result.status = 'success';
      console.log(`✅ ${testName}: Success`);
    } catch (error: any) {
      result.error = {
        message: error.message,
        response: error.response?.data,
        status: error.response?.status,
        validationErrors: error.response?.data?.message
      };
      result.status = 'error';
      console.error(`❌ ${testName}: Failed`, error.response?.data);
    }

    return result;
  };

  const testTransformer = async () => {
    const testInput = {
      name: 'Transformer Test',
      description: 'Testing the transformer',
      imageUrl: 'https://example.com/test.jpg',
      price: 100,
      active: true, // Should be removed
      tenantId: 'tenant-123', // Should be removed
      variations: [
        {
          type: 'CAPSULAS',
          variation: 'Cápsulas',
          sku: 'TRANS-001',
          price: 50,
          costPrice: 25, // Should be removed
          active: true, // Should be removed
          productId: 'prod-123', // Should be removed
          createdAt: new Date(), // Should be removed
          updatedAt: new Date(), // Should be removed
        },
        {
          variation: 'Custom Variation',
          type: 'CUSTOM',
          customName: 'My Custom',
          sku: 'TRANS-002',
          price: 75,
        }
      ]
    };

    const transformed = transformProductForBackend(testInput as any);

    return {
      testName: 'Transformer Function Test',
      input: testInput,
      output: transformed,
      status: 'info'
    };
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckIcon color="success" />;
      case 'error':
        return <ErrorIcon color="error" />;
      case 'info':
        return <WarningIcon color="info" />;
      default:
        return null;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success':
        return 'success.main';
      case 'error':
        return 'error.main';
      case 'info':
        return 'info.main';
      default:
        return 'text.secondary';
    }
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h4" gutterBottom>
        Product Creation Debug Page
      </Typography>
      
      <Alert severity="info" sx={{ mb: 3 }}>
        This page tests various product creation payloads to help identify validation issues.
        Check the browser console (F12) for detailed logs.
      </Alert>

      <Box sx={{ mb: 3 }}>
        <Button 
          variant="contained" 
          onClick={runAllTests}
          sx={{ mr: 2 }}
        >
          Re-run All Tests
        </Button>
        <Button
          variant="outlined"
          onClick={() => setExpandedSections({})}
        >
          Collapse All
        </Button>
      </Box>

      <Stack spacing={3}>
        {testResults.map((result, index) => (
          <Card key={index} elevation={2}>
            <CardContent>
              <Box 
                display="flex" 
                alignItems="center" 
                justifyContent="space-between"
                onClick={() => toggleSection(`test-${index}`)}
                sx={{ cursor: 'pointer' }}
              >
                <Box display="flex" alignItems="center" gap={2}>
                  {getStatusIcon(result.status)}
                  <Typography variant="h6">
                    {result.testName}
                  </Typography>
                  <Chip 
                    label={result.status} 
                    size="small"
                    color={result.status === 'success' ? 'success' : result.status === 'error' ? 'error' : 'default'}
                  />
                </Box>
                <IconButton>
                  <ExpandMoreIcon 
                    sx={{ 
                      transform: expandedSections[`test-${index}`] ? 'rotate(180deg)' : 'none',
                      transition: 'transform 0.3s'
                    }} 
                  />
                </IconButton>
              </Box>

              <Collapse in={expandedSections[`test-${index}`]}>
                <Divider sx={{ my: 2 }} />
                
                <Grid container spacing={2}>
                  {/* Original Payload */}
                  {result.payload && (
                    <Grid item xs={12} md={6}>
                      <Box>
                        <Box display="flex" alignItems="center" justifyContent="space-between" mb={1}>
                          <Typography variant="subtitle2" color="text.secondary">
                            Original Payload
                          </Typography>
                          <IconButton 
                            size="small" 
                            onClick={() => copyToClipboard(JSON.stringify(result.payload, null, 2))}
                          >
                            <CopyIcon fontSize="small" />
                          </IconButton>
                        </Box>
                        <Paper variant="outlined" sx={{ p: 2, bgcolor: 'grey.50' }}>
                          <pre style={{ margin: 0, fontSize: '12px', overflow: 'auto' }}>
                            {JSON.stringify(result.payload, null, 2)}
                          </pre>
                        </Paper>
                      </Box>
                    </Grid>
                  )}

                  {/* Transformed/Sent Payload */}
                  {(result.sentToBackend || result.output) && (
                    <Grid item xs={12} md={6}>
                      <Box>
                        <Box display="flex" alignItems="center" justifyContent="space-between" mb={1}>
                          <Typography variant="subtitle2" color="text.secondary">
                            {result.sentToBackend ? 'Sent to Backend' : 'Transformer Output'}
                          </Typography>
                          <IconButton 
                            size="small" 
                            onClick={() => copyToClipboard(JSON.stringify(result.sentToBackend || result.output, null, 2))}
                          >
                            <CopyIcon fontSize="small" />
                          </IconButton>
                        </Box>
                        <Paper variant="outlined" sx={{ p: 2, bgcolor: 'grey.50' }}>
                          <pre style={{ margin: 0, fontSize: '12px', overflow: 'auto' }}>
                            {JSON.stringify(result.sentToBackend || result.output, null, 2)}
                          </pre>
                        </Paper>
                      </Box>
                    </Grid>
                  )}

                  {/* Response */}
                  {result.response && (
                    <Grid item xs={12}>
                      <Box>
                        <Typography variant="subtitle2" color="text.secondary" mb={1}>
                          Success Response
                        </Typography>
                        <Paper variant="outlined" sx={{ p: 2, bgcolor: 'success.50' }}>
                          <pre style={{ margin: 0, fontSize: '12px', overflow: 'auto' }}>
                            {JSON.stringify(result.response, null, 2)}
                          </pre>
                        </Paper>
                      </Box>
                    </Grid>
                  )}

                  {/* Error */}
                  {result.error && (
                    <Grid item xs={12}>
                      <Box>
                        <Typography variant="subtitle2" color="text.secondary" mb={1}>
                          Error Details
                        </Typography>
                        <Paper variant="outlined" sx={{ p: 2, bgcolor: 'error.50' }}>
                          {result.error.validationErrors && (
                            <Box mb={2}>
                              <Typography variant="body2" fontWeight="bold" color="error.main">
                                Validation Errors:
                              </Typography>
                              {Array.isArray(result.error.validationErrors) ? (
                                <ul style={{ margin: '8px 0', paddingLeft: '20px' }}>
                                  {result.error.validationErrors.map((msg: string, idx: number) => (
                                    <li key={idx}>
                                      <Typography variant="body2" color="error.main">
                                        {msg}
                                      </Typography>
                                    </li>
                                  ))}
                                </ul>
                              ) : (
                                <Typography variant="body2" color="error.main">
                                  {result.error.validationErrors}
                                </Typography>
                              )}
                            </Box>
                          )}
                          <pre style={{ margin: 0, fontSize: '12px', overflow: 'auto' }}>
                            {JSON.stringify(result.error, null, 2)}
                          </pre>
                        </Paper>
                      </Box>
                    </Grid>
                  )}

                  {/* Transformer Test Input/Output */}
                  {result.input && (
                    <Grid item xs={12} md={6}>
                      <Box>
                        <Typography variant="subtitle2" color="text.secondary" mb={1}>
                          Transformer Input
                        </Typography>
                        <Paper variant="outlined" sx={{ p: 2, bgcolor: 'grey.50' }}>
                          <pre style={{ margin: 0, fontSize: '12px', overflow: 'auto' }}>
                            {JSON.stringify(result.input, null, 2)}
                          </pre>
                        </Paper>
                      </Box>
                    </Grid>
                  )}
                </Grid>
              </Collapse>
            </CardContent>
          </Card>
        ))}
      </Stack>

      <Box mt={4}>
        <Typography variant="h6" gutterBottom>
          Expected Backend Structure
        </Typography>
        <Paper variant="outlined" sx={{ p: 2, bgcolor: 'info.50' }}>
          <Typography variant="body2" component="pre" sx={{ fontFamily: 'monospace' }}>
{`CreateProductDto {
  name: string (required)
  description?: string (optional)
  price?: number (optional)
  imageUrl?: string (optional)
  active?: boolean (optional, defaults to true)
  variations: [
    {
      variation: string (required)
      price: number (required)
      sku: string (required)
    }
  ] (at least one required)
}`}
          </Typography>
        </Paper>
      </Box>

      <Box mt={3}>
        <Alert severity="warning">
          <Typography variant="body2" fontWeight="bold" gutterBottom>
            Common Issues:
          </Typography>
          <ul style={{ margin: 0, paddingLeft: '20px' }}>
            <li>Extra fields like 'tenantId', 'kits', 'type', 'costPrice' cause validation errors</li>
            <li>Variations must have 'variation', 'price', and 'sku' fields</li>
            <li>The 'active' field on product is optional but not required</li>
            <li>At least one variation is required</li>
          </ul>
        </Alert>
      </Box>
    </Container>
  );
};

export default ProductCreationDebugPage;