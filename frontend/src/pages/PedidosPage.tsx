import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Paper,
  CircularProgress,
  Button,
} from '@mui/material';
import {
  Add as AddIcon,
} from '@mui/icons-material';
import { useOutletContext } from 'react-router-dom';
import OrdersTableWithPagination from '../components/OrdersTableWithPagination';
import OrderCreationDialog from '../components/OrderCreationDialog';
import OrderService from '../services/OrderService';
import { Order } from '../types/Order';
import { StatusFilter } from '../components/Sidebar';

const PedidosPage: React.FC = () => {
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedStatus, setSelectedStatus] = useState<StatusFilter | null>(null);
  const [filteredOrders, setFilteredOrders] = useState<Order[]>([]);
  const [orderDialogOpen, setOrderDialogOpen] = useState(false);

  // Load orders on mount
  useEffect(() => {
    const loadOrders = async () => {
      try {
        setLoading(true);
        console.log('PedidosPage - Loading orders...');
        const allOrders = await OrderService.getOrders();
        console.log('PedidosPage - Orders loaded:', allOrders?.length);
        setOrders(allOrders);
      } catch (error: any) {
        console.error('PedidosPage - Error loading orders:', error);
        // Check if it's an authentication error
        if (error?.response?.status === 401) {
          console.error('PedidosPage - Authentication error detected');
        }
      } finally {
        setLoading(false);
      }
    };

    loadOrders();
  }, []);

  // Filter orders based on selected status
  useEffect(() => {
    if (!selectedStatus) {
      // Show all orders except deleted ones
      setFilteredOrders(orders.filter(order => 
        order.situacao?.toLowerCase() !== 'deletado'
      ));
    } else if (selectedStatus.field === 'situacao') {
      setFilteredOrders(orders.filter(order =>
        order.situacao?.toLowerCase() === selectedStatus.value.toLowerCase()
      ));
    } else if (selectedStatus.field === 'special' && selectedStatus.value === 'dataRecebimento') {
      const today = new Date().toLocaleDateString('pt-BR');
      setFilteredOrders(orders.filter(order => order.dataRecebimento === today));
    }
  }, [orders, selectedStatus]);

  // Listen for filter updates from sidebar
  useEffect(() => {
    const handleFilterUpdate = (event: CustomEvent) => {
      setSelectedStatus(event.detail);
    };

    window.addEventListener('update-pedidos-filter', handleFilterUpdate as EventListener);

    return () => {
      window.removeEventListener('update-pedidos-filter', handleFilterUpdate as EventListener);
    };
  }, []);

  const handleStatusSelect = (filter: StatusFilter | null) => {
    setSelectedStatus(filter);
  };

  const handleOrderUpdate = (updatedOrder: Order) => {
    setOrders(prevOrders => 
      prevOrders.map(order => 
        order.idVenda === updatedOrder.idVenda ? updatedOrder : order
      )
    );
  };

  const handleCreateOrder = async (orderData: any) => {
    try {
      console.log('Creating order with data:', orderData);
      const newOrder = await OrderService.createNewOrder(orderData);
      console.log('Order created successfully:', newOrder);
      
      // Reload orders to show the new one
      const allOrders = await OrderService.getOrders();
      setOrders(allOrders);
      
      // Close dialog
      setOrderDialogOpen(false);
      
      // Show success message with order ID
      alert(`Pedido criado com sucesso! ID do Pedido: ${newOrder.idVenda}`);
    } catch (error: any) {
      console.error('Error creating order:', error);
      const errorMessage = error.response?.data?.message || error.message || 'Erro ao criar pedido';
      alert(`Erro ao criar pedido: ${errorMessage}`);
    }
  };

  if (loading) {
    return (
      <Container maxWidth="xl">
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="60vh">
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl">
      <Box sx={{ mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h4" component="h1">
            Pedidos
          </Typography>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => setOrderDialogOpen(true)}
          >
            Criar Pedido
          </Button>
        </Box>
        <Typography variant="body1" color="text.secondary">
          {selectedStatus 
            ? `Filtrando por: ${selectedStatus.field === 'situacao' ? selectedStatus.value : 'Receber Hoje'}`
            : 'Todos os pedidos'
          } ({filteredOrders.length} pedidos)
        </Typography>
      </Box>

      <Paper elevation={0} sx={{ backgroundColor: 'transparent' }}>
        <OrdersTableWithPagination
          orders={filteredOrders}
          onOrderUpdate={handleOrderUpdate}
        />
      </Paper>

      <OrderCreationDialog
        open={orderDialogOpen}
        onClose={() => setOrderDialogOpen(false)}
        onSave={handleCreateOrder}
      />
    </Container>
  );
};

export default PedidosPage;