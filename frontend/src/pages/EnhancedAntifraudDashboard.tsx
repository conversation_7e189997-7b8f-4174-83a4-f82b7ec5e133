import React, { useState } from 'react';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Skeleton,
  SpeedDial,
  SpeedDialAction,
  SpeedDialIcon,
  useMediaQuery,
  useTheme,
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  Settings as SettingsIcon,
  GetApp as ExportIcon,
  Refresh as RefreshIcon,
  FilterList as FilterIcon,
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import { AntifraudProvider } from '../components/Antifraud/AntifraudProvider';
import EnhancedReviewQueue from '../components/Antifraud/EnhancedReviewQueue';
import OrderComparisonView from '../components/Antifraud/OrderComparisonView';
import { useAntifraudQueue } from '../hooks/useAntifraudQuery';
import { useAntifraudStore } from '../stores/antifraudStore';

const StatCard: React.FC<{
  title: string;
  value: string | number;
  color?: string;
  loading?: boolean;
}> = ({ title, value, color, loading }) => {
  return (
    <Card>
      <CardContent>
        <Typography color="textSecondary" gutterBottom variant="body2">
          {title}
        </Typography>
        {loading ? (
          <Skeleton variant="text" width="60%" height={40} />
        ) : (
          <Typography variant="h4" component="div" style={{ color }}>
            {value}
          </Typography>
        )}
      </CardContent>
    </Card>
  );
};

const EnhancedAntifraudDashboard: React.FC = () => {
  const { user } = useAuth();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const [comparisonOpen, setComparisonOpen] = useState(false);
  const [selectedOrders, setSelectedOrders] = useState<{ left: any; right: string } | null>(null);
  
  const { data, isLoading } = useAntifraudQueue(1);
  const { refreshQueue } = useAntifraudStore();

  // Check if user has permission
  const hasPermission = user?.role === 'ADMIN' || user?.role === 'SUPERVISOR';

  if (!hasPermission) {
    return (
      <Container maxWidth="lg">
        <Box py={4}>
          <Card>
            <CardContent>
              <Typography variant="h6">Acesso Negado</Typography>
              <Typography variant="body2" color="textSecondary">
                Você não tem permissão para acessar o painel anti-fraude.
              </Typography>
            </CardContent>
          </Card>
        </Box>
      </Container>
    );
  }

  // Calculate statistics
  const stats = {
    pending: data?.items.length || 0,
    approvedToday: 0, // Would come from API
    deniedToday: 0, // Would come from API
    duplicateRate: 0, // Would come from API
  };

  const handleOrderComparison = (leftOrder: any, rightOrderId: string) => {
    setSelectedOrders({ left: leftOrder, right: rightOrderId });
    setComparisonOpen(true);
  };

  return (
    <AntifraudProvider>
      <Container maxWidth="xl">
        <Box py={4}>
          <Typography variant="h4" component="h1" gutterBottom>
            Painel Anti-Fraude Avançado
          </Typography>
          <Typography variant="subtitle1" color="textSecondary" gutterBottom>
            Sistema inteligente de detecção e prevenção de fraudes
          </Typography>

          <Grid container spacing={3} style={{ marginTop: 16 }}>
            {/* Statistics Cards */}
            <Grid item xs={12} sm={6} md={3}>
              <StatCard
                title="Pendentes de Revisão"
                value={stats.pending}
                loading={isLoading}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <StatCard
                title="Aprovados Hoje"
                value={stats.approvedToday}
                color="#4caf50"
                loading={isLoading}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <StatCard
                title="Negados Hoje"
                value={stats.deniedToday}
                color="#f44336"
                loading={isLoading}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <StatCard
                title="Taxa de Duplicação"
                value={`${stats.duplicateRate}%`}
                color="#ff9800"
                loading={isLoading}
              />
            </Grid>

            {/* Main Content Area */}
            <Grid item xs={12} lg={comparisonOpen ? 6 : 12}>
              <EnhancedReviewQueue />
            </Grid>

            {/* Comparison View */}
            {comparisonOpen && selectedOrders && (
              <Grid item xs={12} lg={6}>
                <OrderComparisonView
                  leftOrder={selectedOrders.left}
                  rightOrderId={selectedOrders.right}
                  onClose={() => {
                    setComparisonOpen(false);
                    setSelectedOrders(null);
                  }}
                />
              </Grid>
            )}
          </Grid>

          {/* Speed Dial for quick actions */}
          {!isMobile && (
            <SpeedDial
              ariaLabel="Ações rápidas"
              sx={{ position: 'fixed', bottom: 16, right: 16 }}
              icon={<SpeedDialIcon />}
            >
              <SpeedDialAction
                icon={<RefreshIcon />}
                tooltipTitle="Atualizar"
                onClick={() => refreshQueue()}
              />
              <SpeedDialAction
                icon={<ExportIcon />}
                tooltipTitle="Exportar relatório"
                onClick={() => {/* Export logic */}}
              />
              <SpeedDialAction
                icon={<FilterIcon />}
                tooltipTitle="Filtros avançados"
                onClick={() => {/* Open filter dialog */}}
              />
              <SpeedDialAction
                icon={<SettingsIcon />}
                tooltipTitle="Configurações"
                onClick={() => {/* Open settings */}}
              />
              <SpeedDialAction
                icon={<DashboardIcon />}
                tooltipTitle="Dashboard completo"
                onClick={() => {/* Navigate to full dashboard */}}
              />
            </SpeedDial>
          )}
        </Box>
      </Container>
    </AntifraudProvider>
  );
};

export default EnhancedAntifraudDashboard;