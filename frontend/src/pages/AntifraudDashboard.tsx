import React from 'react';
import { Box, Container, Typography, Grid, Card, CardContent } from '@mui/material';
import { useAuth } from '../contexts/AuthContext';
import DuplicateReviewQueue from '../components/Antifraud/DuplicateReviewQueue';
import { useFeatureFlags } from '../contexts/FeatureFlagContext';
import { Warning as WarningIcon } from '@mui/icons-material';

const AntifraudDashboard: React.FC = () => {
  const { user } = useAuth();
  const { isEnabled } = useFeatureFlags();

  // Check if user has permission
  const hasPermission = user?.role === 'ADMIN' || user?.role === 'SUPERVISOR';

  if (!hasPermission) {
    return (
      <Container maxWidth="lg">
        <Box py={4}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <WarningIcon style={{ marginRight: 8, color: '#f44336' }} />
                <Typography variant="h6">Acesso Negado</Typography>
              </Box>
              <Typography variant="body2" color="textSecondary" style={{ marginTop: 8 }}>
                Você não tem permissão para acessar o painel anti-fraude.
              </Typography>
            </CardContent>
          </Card>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg">
      <Box py={4}>
        <Typography variant="h4" component="h1" gutterBottom>
          Painel Anti-Fraude
        </Typography>
        <Typography variant="subtitle1" color="textSecondary" gutterBottom>
          Gerencie pedidos duplicados e previna fraudes
        </Typography>

        <Grid container spacing={3} style={{ marginTop: 16 }}>
          {/* Statistics Cards */}
          <Grid item xs={12} md={3}>
            <Card>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>
                  Pendentes de Revisão
                </Typography>
                <Typography variant="h4" component="div">
                  -
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={3}>
            <Card>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>
                  Aprovados Hoje
                </Typography>
                <Typography variant="h4" component="div" style={{ color: '#4caf50' }}>
                  -
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={3}>
            <Card>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>
                  Negados Hoje
                </Typography>
                <Typography variant="h4" component="div" style={{ color: '#f44336' }}>
                  -
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={3}>
            <Card>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>
                  Taxa de Duplicação
                </Typography>
                <Typography variant="h4" component="div">
                  -%
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          {/* Review Queue */}
          <Grid item xs={12}>
            <DuplicateReviewQueue />
          </Grid>
        </Grid>
      </Box>
    </Container>
  );
};

export default AntifraudDashboard;