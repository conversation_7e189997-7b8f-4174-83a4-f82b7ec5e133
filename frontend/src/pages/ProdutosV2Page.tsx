import React, { useState, useEffect } from 'react';
import {
  Con<PERSON><PERSON>,
  <PERSON>,
  Typo<PERSON>,
  Button,
  Grid,
  Card,
  CardContent,
  CardActions,
  CardMedia,
  Chip,
  Stack,
  TextField,
  InputAdornment,
  IconButton,
  Avatar,
  Skeleton,
  Alert,
  Fab,
  Zoom,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';
import {
  Add as AddIcon,
  Search as SearchIcon,
  Delete as DeleteIcon,
  MoreVert as MoreVertIcon,
  Image as ImageIcon,
  LocalOffer as LocalOfferIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Archive as ArchiveIcon,
  Unarchive as UnarchiveIcon,
  Visibility as VisibilityIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { getAllProducts, createProduct, updateProduct, deleteProduct } from '../services/ProductService';
import ProductCreationV2Dialog from '../components/ProductCreationV2Dialog';
import { VariationType } from '../types/Product';
import TestProductCreation from '../components/TestProductCreation';

const variationTypeLabels: Record<VariationType, string> = {
  [VariationType.CAPSULAS]: 'Cápsulas',
  [VariationType.GOTAS]: 'Gotas',
  [VariationType.GEL]: 'Gel',
  [VariationType.SPRAY]: 'Spray',
  [VariationType.CREME]: 'Creme',
  [VariationType.CUSTOM]: 'Personalizado',
};

interface Product {
  id: string;
  name: string;
  description?: string;
  imageUrl?: string;
  active: boolean;
  tenantId: string;
  createdAt: Date;
  updatedAt: Date;
  variations: Array<{
    id: string;
    type: VariationType;
    customName?: string;
    costPrice: number;
  }>;
  kits: Array<{
    id: string;
    name: string;
    price: number;
    active: boolean;
  }>;
  _count?: {
    kits: number;
  };
}

const ProdutosV2Page: React.FC = () => {
  const navigate = useNavigate();
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [dialogOpen, setDialogOpen] = useState(false);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [productToDelete, setProductToDelete] = useState<Product | null>(null);

  useEffect(() => {
    fetchProducts();
  }, []);

  const fetchProducts = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await getAllProducts();
      // Ensure data is an array
      if (Array.isArray(data)) {
        setProducts(data);
      } else {
        setProducts([]);
        setError('Formato de dados inválido');
      }
    } catch (err) {
      setError('Erro ao carregar produtos');
      setProducts([]); // Reset to empty array on error
    } finally {
      setLoading(false);
    }
  };

  const handleCreateProduct = async (productData: any) => {
    try {
      console.log('🎯 ProdutosV2Page - handleCreateProduct received:', JSON.stringify(productData, null, 2));
      
      // Ensure we're only sending the expected fields
      const cleanPayload: any = {
        name: productData.name,
        variations: productData.variations,
      };
      
      // Only include optional fields if they exist
      if (productData.description !== undefined) {
        cleanPayload.description = productData.description;
      }
      if (productData.imageUrl !== undefined) {
        cleanPayload.imageUrl = productData.imageUrl;
      }
      if (productData.price !== undefined) {
        cleanPayload.price = productData.price;
      }
      
      console.log('🎯 ProdutosV2Page - Clean payload to send:', JSON.stringify(cleanPayload, null, 2));
      
      await createProduct(cleanPayload);
      await fetchProducts();
      setDialogOpen(false); // Close dialog after success
    } catch (err) {
      setError('Erro ao criar produto: ' + (err as any).message);
      throw err;
    }
  };

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, product: Product) => {
    setAnchorEl(event.currentTarget);
    setSelectedProduct(product);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedProduct(null);
  };

  const handleToggleActive = async (product: Product) => {
    try {
      await updateProduct(product.id, {
        active: !product.active,
      });
      await fetchProducts();
      handleMenuClose();
    } catch (err) {
      console.error('Error toggling product status:', err);
      setError('Erro ao atualizar status do produto');
    }
  };

  const handleDeleteClick = (product: Product) => {
    setProductToDelete(product);
    setDeleteDialogOpen(true);
    handleMenuClose();
  };

  const handleDeleteConfirm = async () => {
    if (!productToDelete) return;

    try {
      await deleteProduct(productToDelete.id);
      await fetchProducts();
      setDeleteDialogOpen(false);
      setProductToDelete(null);
    } catch (err) {
      console.error('Error deleting product:', err);
      setError('Erro ao deletar produto. Verifique se não há kits com vendas.');
    }
  };

  const getProductVariations = (product: Product) => {
    return (product.variations ?? []).map(v => 
      v.type === VariationType.CUSTOM ? v.customName : variationTypeLabels[v.type]
    ).join(', ');
  };

  const getActiveKitsCount = (product: Product) => {
    return (product.kits ?? []).filter(k => k.active).length;
  };

  const filteredProducts = Array.isArray(products) 
    ? products.filter(product =>
        product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.description?.toLowerCase().includes(searchTerm.toLowerCase())
      )
    : [];

  if (loading) {
    return (
      <Container maxWidth="xl">
        <Box sx={{ mb: 4 }}>
          <Skeleton variant="text" width={200} height={40} />
          <Skeleton variant="text" width={300} />
        </Box>
        <Grid container spacing={3}>
          {[1, 2, 3, 4].map((i) => (
            <Grid item xs={12} md={6} lg={4} key={i}>
              <Skeleton variant="rectangular" height={300} />
            </Grid>
          ))}
        </Grid>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl">
      {/* Header */}
      <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center', flexWrap: 'wrap', gap: 2 }}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
            Produtos
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Gerencie seus produtos e crie kits
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <TestProductCreation />
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => setDialogOpen(true)}
            size="large"
          >
            Novo Produto
          </Button>
          {process.env.NODE_ENV === 'development' && (
            <Button
              variant="outlined"
              color="secondary"
              onClick={() => navigate('/produtos/debug')}
              size="large"
            >
              Debug Mode
            </Button>
          )}
        </Box>
      </Box>

      {/* Error Alert */}
      {error && (
        <Alert severity="error" onClose={() => setError(null)} sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Search */}
      <Box sx={{ mb: 3 }}>
        <TextField
          fullWidth
          placeholder="Buscar produtos..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
        />
      </Box>

      {/* Products Grid */}
      <Grid container spacing={3}>
        {filteredProducts.map((product) => (
          <Grid item xs={12} md={6} lg={4} key={product.id}>
            <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
              <CardMedia
                component="div"
                sx={{
                  height: 140,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  bgcolor: 'background.default',
                }}
              >
                {product.imageUrl ? (
                  <Avatar
                    src={product.imageUrl}
                    sx={{ width: 100, height: 100 }}
                  />
                ) : (
                  <ImageIcon sx={{ fontSize: 60, color: 'text.secondary' }} />
                )}
              </CardMedia>
              <CardContent sx={{ flexGrow: 1 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                  <Typography variant="h6" component="h2">
                    {product.name}
                  </Typography>
                  <IconButton size="small" onClick={(e) => handleMenuOpen(e, product)}>
                    <MoreVertIcon />
                  </IconButton>
                </Box>
                
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  {product.description || 'Sem descrição'}
                </Typography>

                <Typography variant="caption" color="text.secondary">
                  Variações:
                </Typography>
                <Typography variant="body2" sx={{ mb: 2 }}>
                  {getProductVariations(product)}
                </Typography>

                <Stack direction="row" spacing={1}>
                  <Chip
                    label={product.active ? 'Ativo' : 'Inativo'}
                    color={product.active ? 'success' : 'default'}
                    size="small"
                    icon={product.active ? <CheckCircleIcon /> : <CancelIcon />}
                  />
                  <Chip
                    label={`${getActiveKitsCount(product)}/${product.kits?.length ?? 0} kits ativos`}
                    size="small"
                    variant="outlined"
                    icon={<LocalOfferIcon />}
                  />
                </Stack>
              </CardContent>
              
              <CardActions>
                <Button
                  fullWidth
                  startIcon={<VisibilityIcon />}
                  onClick={() => navigate(`/dashboard/produtos/${product.id}`)}
                >
                  Ver Detalhes
                </Button>
              </CardActions>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Empty State */}
      {filteredProducts.length === 0 && (
        <Box sx={{ textAlign: 'center', py: 8 }}>
          <ImageIcon sx={{ fontSize: 80, color: 'text.secondary', mb: 2 }} />
          <Typography variant="h6" color="text.secondary" gutterBottom>
            Nenhum produto encontrado
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            {searchTerm ? 'Tente ajustar sua busca' : 'Comece criando seu primeiro produto'}
          </Typography>
          {!searchTerm && (
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => setDialogOpen(true)}
            >
              Criar Produto
            </Button>
          )}
        </Box>
      )}

      {/* Product Creation Dialog */}
      <ProductCreationV2Dialog
        open={dialogOpen}
        onClose={() => setDialogOpen(false)}
        onSave={handleCreateProduct}
      />

      {/* Action Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={() => handleToggleActive(selectedProduct!)}>
          <ListItemIcon>
            {selectedProduct?.active ? <ArchiveIcon fontSize="small" /> : <UnarchiveIcon fontSize="small" />}
          </ListItemIcon>
          <ListItemText>{selectedProduct?.active ? 'Desativar' : 'Ativar'}</ListItemText>
        </MenuItem>
        <Divider />
        <MenuItem onClick={() => handleDeleteClick(selectedProduct!)}>
          <ListItemIcon>
            <DeleteIcon fontSize="small" color="error" />
          </ListItemIcon>
          <ListItemText>Excluir</ListItemText>
        </MenuItem>
      </Menu>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>Confirmar Exclusão</DialogTitle>
        <DialogContent>
          <Typography>
            Tem certeza que deseja excluir o produto "{productToDelete?.name}"?
          </Typography>
          {productToDelete && (productToDelete.kits?.length ?? 0) > 0 && (
            <Alert severity="warning" sx={{ mt: 2 }}>
              Este produto possui {productToDelete.kits?.length ?? 0} kits. Todos serão excluídos.
            </Alert>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancelar</Button>
          <Button onClick={handleDeleteConfirm} color="error" variant="contained">
            Excluir
          </Button>
        </DialogActions>
      </Dialog>

      {/* FAB */}
      <Zoom in={!loading}>
        <Fab
          color="primary"
          aria-label="add"
          sx={{
            position: 'fixed',
            bottom: 24,
            right: 24,
          }}
          onClick={() => setDialogOpen(true)}
        >
          <AddIcon />
        </Fab>
      </Zoom>

    </Container>
  );
};

export default ProdutosV2Page;