import api from './api';

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface AuthTokens {
  access_token: string;
  refresh_token: string;
  token_type: string;
}

export interface UserInfo {
  id: string;
  email: string;
  fullName: string;
  role: string;
  tenantId?: string;
}

/**
 * Unified Authentication Service
 * Uses the centralized API instance for all authentication operations
 */
class UnifiedAuthService {
  private readonly AUTH_TOKENS_KEY = 'unified_auth_tokens';
  private readonly USER_INFO_KEY = 'unified_user_info';
  private readonly TOKEN_EXPIRY_KEY = 'unified_token_expiry';

  /**
   * Login the user
   */
  async login(credentials: LoginCredentials): Promise<AuthTokens> {
    try {
      console.log('UnifiedAuthService: Attempting login for', credentials.email);

      // Use the centralized API instance
      const response = await api.post('/auth/login', {
        email: credentials.email,
        password: credentials.password,
      });

      // Handle nested data structure from backend
      const responseData = response.data?.data || response.data;
      const { access_token, user } = responseData;

      // Create tokens object
      const tokens: AuthTokens = {
        access_token: access_token,
        refresh_token: `refresh-${Date.now()}`, // TODO: Backend should provide refresh token
        token_type: 'bearer'
      };

      // Store tokens
      localStorage.setItem(this.AUTH_TOKENS_KEY, JSON.stringify(tokens));

      // Set token expiry (7 days from now as per JWT_EXPIRATION in backend)
      const expiryTime = Date.now() + (7 * 24 * 60 * 60 * 1000);
      localStorage.setItem(this.TOKEN_EXPIRY_KEY, expiryTime.toString());

      // Store user info
      const userInfo: UserInfo = {
        id: user.id, // Keep as string, backend uses string IDs
        email: user.email,
        fullName: user.fullName || user.name || '',
        role: user.role.toLowerCase(),
        tenantId: user.tenantId || responseData.tenantId
      };

      localStorage.setItem(this.USER_INFO_KEY, JSON.stringify(userInfo));

      // Dispatch login event
      window.dispatchEvent(new CustomEvent('user-login', {
        detail: { email: user.email, role: user.role.toLowerCase() }
      }));

      console.log('UnifiedAuthService: Login successful for', credentials.email);

      return tokens;
    } catch (error: any) {
      console.error('UnifiedAuthService: Login error:', error);
      
      if (error.response?.status === 401) {
        throw new Error('Credenciais inválidas. Por favor, tente novamente.');
      } else if (error.response?.data?.message) {
        throw new Error(error.response.data.message);
      } else {
        throw new Error('Erro no servidor. Por favor, tente novamente.');
      }
    }
  }

  /**
   * Logout the user
   */
  logout(): void {
    // Get user info before removing
    const userInfo = this.getUserInfo();

    // Remove authentication data
    localStorage.removeItem(this.AUTH_TOKENS_KEY);
    localStorage.removeItem(this.USER_INFO_KEY);
    localStorage.removeItem(this.TOKEN_EXPIRY_KEY);

    // Dispatch logout event
    if (userInfo) {
      window.dispatchEvent(new CustomEvent('user-logout', {
        detail: { email: userInfo.email }
      }));
    }

    // Redirect to login page if not already there
    if (!window.location.pathname.includes('/login')) {
      window.location.href = '/login';
    }
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated(): boolean {
    const authTokens = this.getAuthTokens();
    if (!authTokens || !authTokens.access_token) {
      return false;
    }

    // Check if token has expired
    const expiryTimeStr = localStorage.getItem(this.TOKEN_EXPIRY_KEY);
    if (!expiryTimeStr) {
      return false;
    }

    const expiryTime = parseInt(expiryTimeStr, 10);
    const now = Date.now();

    // If token expired, clear data without redirecting
    if (now > expiryTime) {
      console.log('UnifiedAuthService: Token expired');
      localStorage.removeItem(this.AUTH_TOKENS_KEY);
      localStorage.removeItem(this.USER_INFO_KEY);
      localStorage.removeItem(this.TOKEN_EXPIRY_KEY);
      return false;
    }

    return true;
  }

  /**
   * Get authentication tokens
   */
  getAuthTokens(): AuthTokens | null {
    const authTokensStr = localStorage.getItem(this.AUTH_TOKENS_KEY);
    return authTokensStr ? JSON.parse(authTokensStr) : null;
  }

  /**
   * Get authenticated user info
   */
  getUserInfo(): UserInfo | null {
    const userInfoStr = localStorage.getItem(this.USER_INFO_KEY);
    return userInfoStr ? JSON.parse(userInfoStr) : null;
  }

  /**
   * Refresh the access token
   */
  async refreshToken(): Promise<AuthTokens> {
    try {
      const authTokens = this.getAuthTokens();

      if (!authTokens || !authTokens.refresh_token) {
        throw new Error('No refresh token available');
      }

      // TODO: Implement refresh token endpoint in backend
      // For now, we'll just extend the current token
      const userInfo = this.getUserInfo();
      if (!userInfo) {
        throw new Error('No user info available');
      }

      // Extend token expiry
      const expiryTime = Date.now() + (7 * 24 * 60 * 60 * 1000);
      localStorage.setItem(this.TOKEN_EXPIRY_KEY, expiryTime.toString());

      return authTokens;
    } catch (error) {
      console.error('UnifiedAuthService: Error refreshing token:', error);
      this.logout();
      throw error;
    }
  }

  /**
   * Update user profile
   */
  async updateProfile(updates: Partial<UserInfo>): Promise<UserInfo> {
    try {
      const userInfo = this.getUserInfo();
      if (!userInfo) {
        throw new Error('User not authenticated');
      }

      const response = await api.put(`/users/${userInfo.id}`, updates);
      const updatedUser = response.data;

      // Update stored user info
      const newUserInfo: UserInfo = {
        ...userInfo,
        ...updatedUser
      };

      localStorage.setItem(this.USER_INFO_KEY, JSON.stringify(newUserInfo));

      return newUserInfo;
    } catch (error: any) {
      console.error('UnifiedAuthService: Error updating profile:', error);
      throw new Error(error.response?.data?.message || 'Failed to update profile');
    }
  }

  /**
   * Change user password
   */
  async changePassword(currentPassword: string, newPassword: string): Promise<void> {
    try {
      const userInfo = this.getUserInfo();
      if (!userInfo) {
        throw new Error('User not authenticated');
      }

      await api.post('/auth/change-password', {
        currentPassword,
        newPassword
      });
    } catch (error: any) {
      console.error('UnifiedAuthService: Error changing password:', error);
      throw new Error(error.response?.data?.message || 'Failed to change password');
    }
  }
}

export default new UnifiedAuthService();