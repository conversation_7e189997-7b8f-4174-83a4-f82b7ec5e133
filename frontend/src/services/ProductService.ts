import api from './api';
import {
  Product,
  ProductVariation,
  Kit,
  KitItem,
  CreateProductDto,
  UpdateProductDto,
  CreateVariationDto,
  UpdateVariationDto,
  CreateKitDto,
  VariationType,
} from '../types/Product';
import {
  transformProductFromBackend,
  transformProductForBackend,
  transformVariationForBackend,
} from '../utils/productTransformers';

// Debug API configuration
console.log('API base URL =', process.env.REACT_APP_API_URL);

class ProductService {
  // Products
  async getAllProducts(): Promise<Product[]> {
    console.log('🔍 getAllProducts called');
    const response = await api.get('/products');
    console.log('📥 Raw response from backend:', response.data);
    
    // Transform products
    const products = Array.isArray(response.data) 
      ? response.data.map((p: any) => transformProductFromBackend(p))
      : [];
    
    console.log('📦 Transformed products:', products);
    return products;
  }

  async getProduct(id: string): Promise<Product> {
    const response = await api.get(`/products/${id}`);
    return transformProductFromBackend(response.data);
  }

  async createProduct(data: CreateProductDto): Promise<Product> {
    console.log('🎯 ProductService.createProduct called with:', data);
    const backendData = transformProductForBackend(data);
    console.log('📤 Transformed data for backend:', backendData);
    
    const response = await api.post('/products', backendData);
    console.log('📥 Response from backend:', response.data);
    
    return transformProductFromBackend(response.data);
  }

  async updateProduct(id: string, data: UpdateProductDto): Promise<Product> {
    const backendData = transformProductForBackend(data);
    const response = await api.patch(`/products/${id}`, backendData);
    return transformProductFromBackend(response.data);
  }

  async deleteProduct(id: string): Promise<void> {
    await api.delete(`/products/${id}`);
  }

  // Product Variations
  async createVariation(productId: string, data: CreateVariationDto): Promise<ProductVariation> {
    const backendData = transformVariationForBackend(data);
    const response = await api.post(`/products/${productId}/variations`, backendData);
    return response.data;
  }

  async updateVariation(productId: string, variationId: string, data: UpdateVariationDto): Promise<ProductVariation> {
    const backendData = transformVariationForBackend(data);
    const response = await api.patch(`/products/${productId}/variations/${variationId}`, backendData);
    return response.data;
  }

  async deleteVariation(productId: string, variationId: string): Promise<void> {
    await api.delete(`/products/${productId}/variations/${variationId}`);
  }

  // Update stock for a variation
  async updateStock(productId: string, variationId: string, quantity: number, reason: string): Promise<void> {
    await api.post(`/products/${productId}/variations/${variationId}/inventory`, {
      quantity,
      type: quantity > 0 ? 'entrada' : 'saida',
      reason,
    });
  }

  // Kits
  async getAllKits(): Promise<Kit[]> {
    const response = await api.get('/kits');
    return response.data;
  }

  async getKit(id: string): Promise<Kit> {
    const response = await api.get(`/kits/${id}`);
    return response.data;
  }

  async createKit(data: CreateKitDto): Promise<Kit> {
    const response = await api.post('/kits', data);
    return response.data;
  }

  async updateKit(id: string, data: Partial<CreateKitDto>): Promise<Kit> {
    const response = await api.patch(`/kits/${id}`, data);
    return response.data;
  }

  async deleteKit(id: string): Promise<void> {
    await api.delete(`/kits/${id}`);
  }

  // Search functionality
  async searchProducts(query: string): Promise<Product[]> {
    const response = await api.get('/products/search', {
      params: { q: query }
    });
    
    const products = Array.isArray(response.data) 
      ? response.data.map((p: any) => transformProductFromBackend(p))
      : [];
    
    return products;
  }

  // Calculate kit suggested prices
  async calculateKitPrice(items: { productId: string; variationId: string; quantity: number }[]): Promise<{ totalCost: number; suggestedPrice: number }> {
    const response = await api.post('/kits/calculate-price', { items });
    return response.data;
  }
}

// Create a singleton instance
const productService = new ProductService();

// Export both the instance and individual methods
export default productService;

export const getAllProducts = productService.getAllProducts.bind(productService);
export const getProduct = productService.getProduct.bind(productService);
export const createProduct = productService.createProduct.bind(productService);
export const updateProduct = productService.updateProduct.bind(productService);
export const deleteProduct = productService.deleteProduct.bind(productService);
export const createVariation = productService.createVariation.bind(productService);
export const updateVariation = productService.updateVariation.bind(productService);
export const deleteVariation = productService.deleteVariation.bind(productService);
export const updateStock = productService.updateStock.bind(productService);
export const getAllKits = productService.getAllKits.bind(productService);
export const getKit = productService.getKit.bind(productService);
export const createKit = productService.createKit.bind(productService);
export const updateKit = productService.updateKit.bind(productService);
export const deleteKit = productService.deleteKit.bind(productService);
export const searchProducts = productService.searchProducts.bind(productService);
export const calculateKitPrice = productService.calculateKitPrice.bind(productService);