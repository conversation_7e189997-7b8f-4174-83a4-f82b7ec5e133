import api from './api';
import { InventoryItem, InventoryTransaction, ProductType, TransactionType } from '../types/Inventory';

interface BackendInventoryItem {
  id: string;
  productType: ProductType;
  currentStock: number;
  minimumStock: number;
  unitCost: number;
  tenantId: string;
  createdAt: string;
  updatedAt: string;
  _count?: {
    transactions: number;
  };
  transactions?: BackendTransaction[];
}

interface BackendTransaction {
  id: string;
  inventoryItemId: string;
  type: TransactionType;
  quantity: number;
  unitCost?: number;
  totalCost?: number;
  reference?: string;
  notes?: string;
  performedById: string;
  tenantId: string;
  createdAt: string;
  performedBy?: {
    id: string;
    fullName: string;
    email: string;
  };
}

interface InventoryStatistics {
  gel: {
    currentStock: number;
    minimumStock: number;
    unitCost: number;
    totalValue: number;
    lowStock: boolean;
  };
  capsules: {
    currentStock: number;
    minimumStock: number;
    unitCost: number;
    totalValue: number;
    lowStock: boolean;
  };
  transactions: {
    purchases: number;
    sales: number;
    adjustments: number;
    returns: number;
  };
  totalValue: number;
}

class InventoryService {
  /**
   * Get all inventory items
   */
  async getAllItems(): Promise<InventoryItem[]> {
    try {
      const response = await api.get<BackendInventoryItem[]>('/inventory');
      return response.data.map(this.convertToFrontendItem);
    } catch (error) {
      console.error('Error fetching inventory items:', error);
      return [];
    }
  }

  /**
   * Get inventory item by product type
   */
  async getItem(productType: ProductType): Promise<InventoryItem | null> {
    try {
      const response = await api.get<BackendInventoryItem>(`/inventory/${productType}`);
      return this.convertToFrontendItem(response.data);
    } catch (error) {
      console.error('Error fetching inventory item:', error);
      return null;
    }
  }

  /**
   * Update inventory settings
   */
  async updateItem(productType: ProductType, updates: { minimumStock?: number; unitCost?: number }): Promise<InventoryItem | null> {
    try {
      const response = await api.patch<BackendInventoryItem>(`/inventory/${productType}`, updates);
      return this.convertToFrontendItem(response.data);
    } catch (error) {
      console.error('Error updating inventory item:', error);
      return null;
    }
  }

  /**
   * Create inventory transaction
   */
  async createTransaction(transaction: Omit<InventoryTransaction, 'id' | 'timestamp' | 'performedBy'>): Promise<InventoryTransaction | null> {
    try {
      const response = await api.post<BackendTransaction>('/inventory/transactions', {
        productType: transaction.productType,
        type: transaction.type,
        quantity: transaction.quantity,
        unitCost: transaction.unitCost,
        reference: transaction.reference,
        notes: transaction.notes,
      });
      return this.convertToFrontendTransaction(response.data);
    } catch (error) {
      console.error('Error creating transaction:', error);
      throw error; // Re-throw to handle insufficient stock errors
    }
  }

  /**
   * Get inventory statistics
   */
  async getStatistics(): Promise<InventoryStatistics | null> {
    try {
      const response = await api.get<InventoryStatistics>('/inventory/statistics');
      return response.data;
    } catch (error) {
      console.error('Error fetching inventory statistics:', error);
      return null;
    }
  }

  /**
   * Get low stock items
   */
  async getLowStockItems(): Promise<InventoryItem[]> {
    try {
      const response = await api.get<BackendInventoryItem[]>('/inventory/low-stock');
      return response.data.map(this.convertToFrontendItem);
    } catch (error) {
      console.error('Error fetching low stock items:', error);
      return [];
    }
  }

  /**
   * Get transactions for a product type
   */
  async getTransactions(productType: ProductType, filters?: {
    type?: TransactionType;
    startDate?: Date;
    endDate?: Date;
  }): Promise<InventoryTransaction[]> {
    try {
      const item = await this.getItem(productType);
      if (!item || !item.transactions) return [];

      let transactions = item.transactions;

      // Apply filters
      if (filters) {
        if (filters.type) {
          transactions = transactions.filter(t => t.type === filters.type);
        }
        if (filters.startDate) {
          transactions = transactions.filter(t => new Date(t.timestamp) >= filters.startDate!);
        }
        if (filters.endDate) {
          transactions = transactions.filter(t => new Date(t.timestamp) <= filters.endDate!);
        }
      }

      return transactions;
    } catch (error) {
      console.error('Error fetching transactions:', error);
      return [];
    }
  }

  /**
   * Convert backend item to frontend format
   */
  private convertToFrontendItem(backendItem: BackendInventoryItem): InventoryItem {
    return {
      productType: backendItem.productType,
      currentStock: backendItem.currentStock,
      minimumStock: backendItem.minimumStock,
      unitCost: backendItem.unitCost,
      transactions: backendItem.transactions?.map(t => this.convertToFrontendTransaction(t)) || [],
    };
  }

  /**
   * Convert backend transaction to frontend format
   */
  private convertToFrontendTransaction(backendTx: BackendTransaction): InventoryTransaction {
    return {
      id: backendTx.id,
      productType: backendTx.inventoryItemId.includes('GEL') ? ProductType.GEL : ProductType.CAPSULES,
      type: backendTx.type,
      quantity: backendTx.quantity,
      unitCost: backendTx.unitCost,
      totalCost: backendTx.totalCost,
      reference: backendTx.reference,
      notes: backendTx.notes,
      timestamp: backendTx.createdAt,
      performedBy: backendTx.performedBy?.fullName || 'Sistema',
    };
  }
}

export default new InventoryService();