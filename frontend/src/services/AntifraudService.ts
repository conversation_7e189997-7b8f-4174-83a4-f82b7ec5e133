import UnifiedAuthService from './UnifiedAuthService';

export interface DuplicateReviewItem {
  id: string;
  orderNumber: string;
  customerName: string;
  customerPhone: string;
  total: number;
  createdAt: string;
  duplicateMatchScore: number;
  matchedOrders: Array<{
    orderId: string;
    orderNumber: string;
    createdAt: string;
  }>;
}

export interface ReviewDecision {
  decision: 'APPROVE_ORDER' | 'DENY_ORDER' | 'MERGE_ORDERS' | 'INVESTIGATE_FURTHER';
  notes?: string;
}

export interface AuditLogEntry {
  id: string;
  orderId: string;
  action: string;
  performedBy: string;
  performedByName: string;
  performedByRole: string;
  performedAt: string;
  previousData?: any;
  newData?: any;
  metadata?: any;
  signatureValid: boolean;
}

class AntifraudService {
  private static instance: AntifraudService;

  private constructor() {}

  static getInstance(): AntifraudService {
    if (!AntifraudService.instance) {
      AntifraudService.instance = new AntifraudService();
    }
    return AntifraudService.instance;
  }

  private async makeRequest(
    method: string,
    endpoint: string,
    data?: any
  ): Promise<any> {
    const tenantId = localStorage.getItem('tenantId');
    const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:3001';

    const response = await fetch(`${apiUrl}${endpoint}`, {
      method,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${UnifiedAuthService.getToken()}`,
        'x-tenant-id': tenantId || '',
      },
      body: data ? JSON.stringify(data) : undefined,
    });

    if (!response.ok) {
      const error = await response.json().catch(() => ({ message: 'Request failed' }));
      throw new Error(error.message || `HTTP error! status: ${response.status}`);
    }

    return response.json();
  }

  async getDuplicateReviewQueue(page: number = 1, limit: number = 20): Promise<{
    items: DuplicateReviewItem[];
    total: number;
    pages: number;
  }> {
    return this.makeRequest(
      'GET',
      `/antifraud/duplicates/review-queue?page=${page}&limit=${limit}`
    );
  }

  async reviewDuplicate(
    orderId: string,
    review: ReviewDecision
  ): Promise<{ success: boolean; message: string }> {
    return this.makeRequest(
      'POST',
      `/antifraud/duplicates/${orderId}/review`,
      review
    );
  }

  async getOrderAuditTrail(orderId: string): Promise<AuditLogEntry[]> {
    return this.makeRequest(
      'GET',
      `/antifraud/orders/${orderId}/audit-trail`
    );
  }

  formatMatchScore(score: number): string {
    return `${Math.round(score)}%`;
  }

  getMatchScoreColor(score: number): string {
    if (score >= 80) return '#d32f2f'; // High match - Red
    if (score >= 60) return '#f57c00'; // Medium match - Orange
    return '#388e3c'; // Low match - Green
  }

  getMatchScoreLabel(score: number): string {
    if (score >= 80) return 'Alta Similaridade';
    if (score >= 60) return 'Média Similaridade';
    return 'Baixa Similaridade';
  }
}

export default AntifraudService.getInstance();