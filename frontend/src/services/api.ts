import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';

// Estender a interface AxiosInstance para incluir os métodos personalizados
interface EnhancedAxiosInstance extends AxiosInstance {
  getData<T>(endpoint: string, config?: AxiosRequestConfig): Promise<T>;
  postData<T>(endpoint: string, data?: any, config?: AxiosRequestConfig): Promise<T>;
}

// Criando uma instância axios com configurações padrão
const api = axios.create({
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:3000/api/v1',
  headers: {
    'Content-Type': 'application/json'
  },
  timeout: 10000 // 10 segundos de timeout
}) as EnhancedAxiosInstance;

// Interceptor para adicionar token de autenticação se disponível
api.interceptors.request.use(config => {
  // Get token from UnifiedAuthService
  const authTokens = localStorage.getItem('unified_auth_tokens');
  const token = authTokens ? JSON.parse(authTokens).access_token : null;

  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
    console.log('API Request - Added auth token for:', config.url);
  } else {
    console.log('API Request - No auth token for:', config.url);
  }

  // Add tenant ID from user info or environment
  if (!config.url?.includes('/auth/login')) {
    // Try to get tenant ID from user info first
    const userInfo = localStorage.getItem('unified_user_info');
    let tenantId = process.env.REACT_APP_TENANT_ID || 'acme-corp';
    
    if (userInfo) {
      try {
        const parsed = JSON.parse(userInfo);
        if (parsed.tenantId) {
          tenantId = parsed.tenantId;
        }
      } catch (e) {
        console.error('Failed to parse user info for tenant ID');
      }
    }
    
    config.headers['x-tenant-id'] = tenantId;
    
    // Debug logging
    console.log('API Request:', config.method?.toUpperCase(), config.url, 'with tenant:', tenantId);
  } else {
    // For login, we need to get tenant ID from the domain
    const tenantId = process.env.REACT_APP_TENANT_ID || 'cmc4a2wg50000uvkzj76t0mpt';
    config.headers['x-tenant-id'] = tenantId;
    console.log('API Request:', config.method?.toUpperCase(), config.url, 'with tenant:', tenantId);
  }

  return config;
}, error => {
  return Promise.reject(error);
});

// Interceptor de resposta para tratamento de erros e refresh de token
api.interceptors.response.use(
  response => response,
  async error => {
    const originalRequest = error.config;

    // Se o erro for 401 (não autorizado) e não for uma tentativa de refresh
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      // Check if we're already on the login page to avoid redirect loops
      if (window.location.pathname === '/login') {
        return Promise.reject(error);
      }

      try {
        // Import UnifiedAuthService dynamically to avoid circular dependencies
        const { default: UnifiedAuthService } = await import('./UnifiedAuthService');
        
        console.log('Token expired, logging out');
        UnifiedAuthService.logout();
        
        return Promise.reject(error);
      } catch (refreshError) {
        console.error('Erro ao fazer logout:', refreshError);
        
        // Clear auth data and redirect manually
        localStorage.removeItem('unified_auth_tokens');
        localStorage.removeItem('unified_user_info');
        localStorage.removeItem('unified_token_expiry');
        window.location.href = '/login';
      }
    }

    return Promise.reject(error);
  }
);

// Função de helper para extrair somente os dados da resposta
api.getData = async function<T>(endpoint: string, config = {}): Promise<T> {
  const response = await api.get<T>(endpoint, config);
  return response.data;
};

// Função de helper para criar e extrair dados
api.postData = async function<T>(endpoint: string, data = {}, config = {}): Promise<T> {
  const response = await api.post<T>(endpoint, data, config);
  return response.data;
};

export default api;