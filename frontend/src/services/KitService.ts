import axios, { AxiosInstance } from 'axios';

class KitService {
  public api: AxiosInstance;

  constructor() {
    const baseURL = process.env.REACT_APP_API_URL || 'http://localhost:3000/api/v1';
    
    this.api = axios.create({
      baseURL,
      headers: {
        'Content-Type': 'application/json',
      },
      withCredentials: true,
    });

    // Add request interceptor for auth and tenant ID
    this.api.interceptors.request.use((config) => {
      // Get auth token
      const authTokens = localStorage.getItem('unified_auth_tokens');
      const token = authTokens ? JSON.parse(authTokens).access_token : null;
      
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }

      // Get tenant ID
      const userInfo = localStorage.getItem('unified_user_info');
      const tenantId = userInfo ? JSON.parse(userInfo).tenantId : null;
      if (tenantId) {
        config.headers['x-tenant-id'] = tenantId;
      }

      return config;
    });
  }

  async getAllKits(): Promise<any[]> {
    try {
      const response = await this.api.get('/kits');
      return response.data;
    } catch (error) {
      console.error('Error fetching kits:', error);
      throw error;
    }
  }

  async getKitsByProductId(productId: string): Promise<any[]> {
    try {
      // Get all kits
      const allKits = await this.getAllKits();
      
      // Filter kits that contain variations from this product
      // This is a client-side filter since backend doesn't support this
      const productKits = allKits.filter(kit => 
        kit.items?.some((item: any) => 
          item.productVariation?.productId === productId
        )
      );
      
      return productKits;
    } catch (error) {
      console.error('Error fetching kits for product:', error);
      throw error;
    }
  }

  async updateKit(kitId: string, data: any): Promise<any> {
    try {
      const response = await this.api.patch(`/kits/${kitId}`, data);
      return response.data;
    } catch (error) {
      console.error('Error updating kit:', error);
      throw error;
    }
  }

  async deleteKit(kitId: string): Promise<void> {
    try {
      await this.api.delete(`/kits/${kitId}`);
    } catch (error) {
      console.error('Error deleting kit:', error);
      throw error;
    }
  }
}

export const kitService = new KitService();
export default KitService;