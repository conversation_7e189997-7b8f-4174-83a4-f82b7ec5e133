import React, { ReactNode } from 'react';
import { Navigate, RouteObject, Outlet } from 'react-router-dom';
import UnifiedAuthService from '../services/UnifiedAuthService';
import { UserRole } from '../types/User';

// Import Layout component
import Layout from '../components/Layout';

// Import pages directly for now (we'll implement lazy loading later)
import LandingPage from '../pages/LandingPage';
import UnifiedLoginPage from '../pages/UnifiedLoginPage';
import PasswordResetPage from '../pages/PasswordResetPage';
import DashboardPage from '../pages/DashboardPage';
import AdminDashboardPage from '../pages/AdminDashboardPage';
import ReportsPage from '../pages/ReportsPage';
import AdvancedReportsPage from '../pages/AdvancedReportsPage';
import TrackingPage from '../pages/TrackingPage';
import DuplicateOrdersPage from '../pages/DuplicateOrdersPage';
import SettingsPage from '../pages/SettingsPage';
import ExamplePage from '../pages/ExamplePage';
import UnifiedUsersPage from '../pages/UnifiedUsersPage';
import UnifiedNewUserPage from '../pages/UnifiedNewUserPage';
import UnifiedEditUserPage from '../pages/UnifiedEditUserPage';
import ImportPage from '../pages/ImportPage';
import RestoreDataPage from '../pages/RestoreDataPage';
import WebhookPage from '../pages/WebhookPage';
import CorreiosApiManagementPage from '../pages/CorreiosApiManagementPage';
import VendedorRankingPage from '../pages/VendedorRankingPage';
import OperadorRankingPage from '../pages/OperadorRankingPage';
import TrackingManagementPage from '../pages/TrackingManagementPage';
import ZapConfigPage from '../pages/ZapConfigPage';
import ProfilePage from '../pages/ProfilePage';
import PedidosPage from '../pages/PedidosPage';
import ProdutosV2Page from '../pages/ProdutosV2Page';
import ProductCreationDebugPage from '../pages/ProductCreationDebugPage';
import ProductDetailV2Page from '../pages/ProductDetailV2Page';
import TenantRegisterPage from '../pages/TenantRegisterPage';
import TenantDashboardPage from '../pages/TenantDashboardPage';
import AntifraudDashboard from '../pages/AntifraudDashboard';

// Auth guard component
interface AuthGuardProps {
  children?: ReactNode;
  requiredRole?: UserRole | UserRole[];
}

const AuthGuard: React.FC<AuthGuardProps> = ({ children, requiredRole }) => {
  const isAuthenticated = UnifiedAuthService.isAuthenticated();

  if (!isAuthenticated) {
    return <Navigate to="/login" />;
  }

  if (requiredRole) {
    const userInfo = UnifiedAuthService.getUserInfo();
    const userRole = userInfo?.role || '';
    const roleArray = Array.isArray(requiredRole) ? requiredRole : [requiredRole];
    const hasRequiredRole = roleArray.includes(userRole as UserRole);
    if (!hasRequiredRole) {
      return <Navigate to="/dashboard" />;
    }
  }

  return <>{children || <Outlet />}</>;
};

// Define routes
export const routes: RouteObject[] = [
  // Landing page - no auth required
  {
    path: '/',
    element: <LandingPage />,
  },
  // Routes without layout (login and password reset)
  {
    path: '/login',
    element: <UnifiedLoginPage />,
  },
  {
    path: '/reset-password',
    element: <PasswordResetPage />,
  },
  // Tenant registration - no auth required
  {
    path: '/register-tenant',
    element: <TenantRegisterPage />,
  },
  // Protected routes with layout
  {
    path: '/dashboard',
    element: (
      <AuthGuard>
        <Layout />
      </AuthGuard>
    ),
    children: [
      {
        index: true,
        element: <DashboardPage />,
      },
      {
        path: 'pedidos',
        element: <PedidosPage />,
      },
      {
        path: 'produtos',
        element: <ProdutosV2Page />,
      },
      {
        path: 'produtos/debug',
        element: <ProductCreationDebugPage />,
      },
      {
        path: 'produtos/:id',
        element: <ProductDetailV2Page />,
      },
      {
        path: 'admin',
        element: (
          <AuthGuard requiredRole="admin">
            <AdminDashboardPage />
          </AuthGuard>
        ),
      },
      {
        path: 'reports',
        element: (
          <AuthGuard requiredRole={['admin', 'supervisor']}>
            <ReportsPage />
          </AuthGuard>
        ),
      },
      {
        path: 'tracking',
        element: <TrackingPage />,
      },
      {
        path: 'duplicates',
        element: (
          <AuthGuard requiredRole={['admin', 'supervisor']}>
            <DuplicateOrdersPage />
          </AuthGuard>
        ),
      },
      {
        path: 'antifraud',
        element: (
          <AuthGuard requiredRole={['admin', 'supervisor']}>
            <AntifraudDashboard />
          </AuthGuard>
        ),
      },
      {
        path: 'settings',
        element: (
          <AuthGuard requiredRole="admin">
            <SettingsPage />
          </AuthGuard>
        ),
      },
      {
        path: 'example',
        element: <ExamplePage />,
      },
      {
        path: 'advanced-reports',
        element: (
          <AuthGuard requiredRole={['admin', 'supervisor']}>
            <AdvancedReportsPage />
          </AuthGuard>
        ),
      },
      {
        path: 'users',
        element: (
          <AuthGuard requiredRole={['admin', 'supervisor']}>
            <UnifiedUsersPage />
          </AuthGuard>
        ),
      },
      {
        path: 'users/new',
        element: (
          <AuthGuard requiredRole={['admin', 'supervisor']}>
            <UnifiedNewUserPage />
          </AuthGuard>
        ),
      },
      {
        path: 'users/:id/edit',
        element: (
          <AuthGuard requiredRole={['admin', 'supervisor']}>
            <UnifiedEditUserPage />
          </AuthGuard>
        ),
      },
      {
        path: 'import',
        element: (
          <AuthGuard requiredRole={['admin', 'supervisor']}>
            <ImportPage onImportSuccess={() => console.log('Import success')} />
          </AuthGuard>
        ),
      },
      {
        path: 'restore-data',
        element: (
          <AuthGuard requiredRole="admin">
            <RestoreDataPage />
          </AuthGuard>
        ),
      },
      {
        path: 'webhook',
        element: (
          <AuthGuard requiredRole="admin">
            <WebhookPage />
          </AuthGuard>
        ),
      },
      {
        path: 'correios-api',
        element: (
          <AuthGuard requiredRole="admin">
            <CorreiosApiManagementPage />
          </AuthGuard>
        ),
      },
      {
        path: 'ranking',
        element: <VendedorRankingPage />,
      },
      {
        path: 'ranking-operador',
        element: (
          <AuthGuard requiredRole={['admin', 'supervisor']}>
            <OperadorRankingPage />
          </AuthGuard>
        ),
      },
      {
        path: 'tracking-management',
        element: (
          <AuthGuard requiredRole="admin">
            <TrackingManagementPage />
          </AuthGuard>
        ),
      },
      {
        path: 'zap-config',
        element: (
          <AuthGuard requiredRole="admin">
            <ZapConfigPage />
          </AuthGuard>
        ),
      },
      {
        path: 'profile',
        element: <ProfilePage />,
      },
      {
        path: 'tenant-management',
        element: (
          <AuthGuard requiredRole="admin">
            <TenantDashboardPage />
          </AuthGuard>
        ),
      },
    ],
  },
  // Catch-all redirect
  {
    path: '*',
    element: <Navigate to="/dashboard" replace />,
  },
];

// Preload critical routes (placeholder for now)
export const preloadCriticalRoutes = () => {
  // We'll implement this later with proper lazy loading
  console.log('Preloading critical routes...');
};
