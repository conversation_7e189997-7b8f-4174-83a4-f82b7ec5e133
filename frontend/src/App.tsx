import React, { useState, useEffect } from 'react';
import { createBrowserRouter, RouterProvider } from 'react-router-dom';
import CssBaseline from '@mui/material/CssBaseline';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { ptBR } from 'date-fns/locale';
import './utils/testConnection'; // Import test utility
import './utils/windowDebug'; // Import debug utilities

// Import custom contexts
import { ThemeProvider } from './contexts/ThemeContext';
import { NotificationProvider } from './contexts/NotificationContext';
import { UnifiedUserProvider } from './contexts/UnifiedUserContext';
import { AuthProvider } from './contexts/AuthContext';
import { OrderDataProvider } from './contexts/OrderDataContext';
import { ConversionProvider } from './contexts/ConversionContext';
import { FeatureFlagProvider } from './contexts/FeatureFlagContext';
import ErrorBoundary from './components/ErrorBoundary';
import globalStyles from './components/GlobalStyles';

// Import routes
import { routes } from './routes/routes';

// Services
import UnifiedAuthService from './services/UnifiedAuthService';
// import CorreiosService, { StatusUpdateResult } from './services/CorreiosService';
import { Order } from './types/Order';

// Development helpers
import SetupTestData from './components/SetupTestData';

function App() {
  const [orders, setOrders] = useState<Order[]>([]);

  // Check authentication on app start
  useEffect(() => {
    try {
      const isAuth = UnifiedAuthService.isAuthenticated();
      console.log(`Verificação de autenticação: ${isAuth ? 'Autenticado' : 'Não autenticado'}`);

      if (isAuth) {
        const userInfo = UnifiedAuthService.getUserInfo();
        console.log(`Usuário autenticado: ${userInfo?.fullName} (${userInfo?.email})`);
      }
    } catch (error) {
      console.error("Erro ao verificar autenticação:", error);
    }
  }, []);

  // Load orders from localStorage
  useEffect(() => {
    const storedOrders = localStorage.getItem('orders');
    if (storedOrders) {
      try {
        const parsedOrders = JSON.parse(storedOrders);
        setOrders(parsedOrders);
      } catch (error) {
        console.error('Error parsing stored orders:', error);
      }
    }
  }, []);

  // Check for tracking updates periodically
  useEffect(() => {
    if (!UnifiedAuthService.isAuthenticated() || orders.length === 0) {
      return;
    }

    const checkTrackingUpdates = async () => {
      try {
        const ordersWithTracking = orders.filter(order => order.codigoRastreio);
        if (ordersWithTracking.length === 0) return;

        // Commented out for now since the method doesn't exist in CorreiosService
        // TODO: Implement tracking updates functionality
        /*
        const updates = await CorreiosService.verificarAtualizacoes(
          ordersWithTracking.map(o => ({
            idVenda: o.idVenda,
            codigoRastreio: o.codigoRastreio!,
            statusCorreios: o.statusCorreios
          }))
        );

        if (updates && updates.length > 0) {
          const updatedOrders = orders.map(order => {
            const update = updates.find((u: StatusUpdateResult) => u.orderId === order.idVenda);
            if (update) {
              return {
                ...order,
                statusCorreios: update.status
              };
            }
            return order;
          });

          setOrders(updatedOrders);
          localStorage.setItem('orders', JSON.stringify(updatedOrders));
        }
        */
      } catch (error) {
        console.error('Error checking tracking updates:', error);
      }
    };

    // Check for updates every 30 minutes
    const interval = setInterval(checkTrackingUpdates, 30 * 60 * 1000);

    // Initial check
    checkTrackingUpdates();

    return () => clearInterval(interval);
  }, [orders]);

  // Create router with routes
  const router = createBrowserRouter(routes);

  return (
    <ErrorBoundary>
      <ThemeProvider>
        <NotificationProvider>
          <FeatureFlagProvider>
            <UnifiedUserProvider>
              <AuthProvider>
                <OrderDataProvider>
                  <ConversionProvider>
                    <CssBaseline />
                    {globalStyles}
                    <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={ptBR}>
                      <RouterProvider router={router} />
                      {process.env.NODE_ENV === 'development' && <SetupTestData />}
                    </LocalizationProvider>
                  </ConversionProvider>
                </OrderDataProvider>
              </AuthProvider>
            </UnifiedUserProvider>
          </FeatureFlagProvider>
        </NotificationProvider>
      </ThemeProvider>
    </ErrorBoundary>
  );
}

export default App;// Force rebuild Sat Jul  5 15:18:34 -03 2025
