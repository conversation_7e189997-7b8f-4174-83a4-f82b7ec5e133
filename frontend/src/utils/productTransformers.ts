import {
  Product,
  ProductVariation,
  Kit,
  KitItem,
  VariationType,
  CreateProductDto,
  CreateVariationDto,
  Inventory,
} from '../types/Product';

// Map backend variation names to frontend types
const variationTypeMap: Record<string, VariationType> = {
  'Cápsulas': VariationType.CAPSULAS,
  'Gotas': VariationType.GOTAS,
  'Gel': VariationType.GEL,
  'Spray': VariationType.SPRAY,
  'Creme': VariationType.CREME,
};

/**
 * Transform raw backend product data to frontend Product type
 */
export function transformProductFromBackend(data: any): Product {
  // Get tenant ID from various sources
  const tenantId = data.tenantId || 
    getTenantIdFromStorage() || 
    process.env.REACT_APP_TENANT_ID || 
    'default-tenant';

  return {
    id: data.id,
    name: data.name,
    description: data.description || undefined,
    imageUrl: data.imageUrl || undefined,
    active: data.active ?? true,
    tenantId: tenantId,
    createdAt: parseDate(data.createdAt),
    updatedAt: parseDate(data.updatedAt),
    variations: Array.isArray(data.variations) 
      ? data.variations.map((v: any) => transformVariationFromBackend(v, data.id))
      : [],
    kits: Array.isArray(data.kits)
      ? data.kits.map((k: any) => transformKitFromBackend(k, data.id))
      : [],
    _count: {
      variations: data._count?.variations || data.variations?.length || 0,
      kits: data._count?.kits || data.kits?.length || 0,
    },
  };
}

/**
 * Transform raw backend variation data to frontend ProductVariation type
 */
export function transformVariationFromBackend(data: any, productId: string): ProductVariation {
  // Determine variation type from name
  const type = variationTypeMap[data.variation] || VariationType.CUSTOM;
  
  const priceValue = parseFloat(data.price) || 0;
  
  return {
    id: data.id,
    productId: data.productId || productId,
    variation: data.variation, // Display name
    type: type,
    customName: type === VariationType.CUSTOM ? data.variation : undefined,
    sku: data.sku || '',
    price: priceValue,
    costPrice: priceValue, // Same value for UI compatibility
    active: data.active ?? true,
    inventory: data.inventory ? transformInventoryFromBackend(data.inventory) : undefined,
    createdAt: parseDate(data.createdAt),
    updatedAt: parseDate(data.updatedAt),
  };
}

/**
 * Transform raw backend inventory data to frontend Inventory type
 */
export function transformInventoryFromBackend(data: any): Inventory {
  return {
    id: data.id,
    quantity: parseInt(data.quantity, 10) || 0,
    minAlert: parseInt(data.minAlert, 10) || 10,
    productVariationId: data.productVariationId,
    updatedAt: parseDate(data.updatedAt),
  };
}

/**
 * Transform raw backend kit data to frontend Kit type
 */
export function transformKitFromBackend(data: any, productId: string): Kit {
  return {
    id: data.id,
    productId: data.productId || productId,
    sku: data.sku || '',
    name: data.name,
    description: data.description || undefined,
    price: parseFloat(data.price) || 0,
    active: data.active ?? true,
    items: Array.isArray(data.items)
      ? data.items.map((item: any) => transformKitItemFromBackend(item))
      : [],
    product: data.product ? transformProductFromBackend(data.product) : undefined,
    createdAt: parseDate(data.createdAt),
    updatedAt: parseDate(data.updatedAt),
    _count: data._count || { orders: 0 },
  };
}

/**
 * Transform raw backend kit item data to frontend KitItem type
 */
export function transformKitItemFromBackend(data: any): KitItem {
  if (!data.variation) {
    throw new Error(`KitItem ${data.id} missing required variation data`);
  }
  
  return {
    id: data.id,
    kitId: data.kitId,
    variationId: data.variationId,
    quantity: parseInt(data.quantity, 10) || 1,
    variation: transformVariationFromBackend(data.variation, data.variation.productId),
  };
}

/**
 * Transform frontend product data for backend API
 */
export function transformProductForBackend(product: any): any {
  console.log('🔄 transformProductForBackend input:', JSON.stringify(product, null, 2));
  
  // Start with only the fields that the backend expects
  const transformed: any = {
    name: product.name,
  };
  
  // Only include optional fields if they have values (not undefined)
  if (product.description !== undefined) {
    transformed.description = product.description;
  }
  
  if (product.imageUrl !== undefined) {
    transformed.imageUrl = product.imageUrl;
  }
  
  if (product.price !== undefined) {
    transformed.price = product.price;
  }
  
  // Transform variations - required field
  if (product.variations && product.variations.length > 0) {
    transformed.variations = product.variations.map((v: any) => {
      console.log('🔄 transformVariationForBackend input:', JSON.stringify(v, null, 2));
      const result = transformVariationForBackend(v);
      console.log('🔄 transformVariationForBackend output:', JSON.stringify(result, null, 2));
      return result;
    });
  }
  
  // Explicitly DO NOT include these fields that might be present in the input:
  // - active (backend sets default)
  // - tenantId (sent via header)
  // - kits (created separately)
  // - type (not a product field)
  // - costPrice (not a product field)
  // - createdAt/updatedAt (managed by backend)
  // - id (generated by backend)
  // - _count (computed field)
  
  console.log('🔄 transformProductForBackend output:', JSON.stringify(transformed, null, 2));
  return transformed;
}

/**
 * Transform frontend variation data for backend API
 */
export function transformVariationForBackend(variation: any): any {
  // Handle variations coming from ProductCreationV2Dialog which have a 'type' field
  let variationName = variation.variation;
  
  // If variation name is not set but type is, derive it
  if (!variationName && variation.type) {
    if (variation.type === 'CUSTOM' && variation.customName) {
      variationName = variation.customName;
    } else if (variationTypeMap[variation.type]) {
      variationName = variationTypeMap[variation.type];
    } else {
      variationName = variation.type;
    }
  }
  
  // Return ONLY the fields that the backend expects for variations
  // Backend expects exactly: variation (string), sku (string), price (number)
  return {
    variation: variationName || '',
    sku: variation.sku || '',
    price: typeof variation.price === 'number' ? variation.price : 0,
  };
  
  // Explicitly NOT including:
  // - type (frontend only)
  // - customName (frontend only)
  // - costPrice (not in backend DTO)
  // - active (not in creation DTO)
  // - productId (set by backend)
  // - id (generated by backend)
  // - createdAt/updatedAt (managed by backend)
  // - inventory (created separately)
}

/**
 * Parse date string to Date object safely
 */
function parseDate(dateValue: any): Date {
  if (!dateValue) return new Date();
  if (dateValue instanceof Date) return dateValue;
  
  const parsed = new Date(dateValue);
  return isNaN(parsed.getTime()) ? new Date() : parsed;
}

/**
 * Get tenant ID from localStorage
 */
function getTenantIdFromStorage(): string | null {
  try {
    const userInfo = localStorage.getItem('unified_user_info');
    if (userInfo) {
      const parsed = JSON.parse(userInfo);
      return parsed.tenantId || null;
    }
  } catch (e) {
    console.error('Error parsing user info:', e);
  }
  return null;
}