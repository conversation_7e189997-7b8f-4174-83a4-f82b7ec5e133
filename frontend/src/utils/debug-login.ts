export const debugLogin = async () => {
  console.log('=== LOGIN DEBUG ===');
  
  // Check environment variables
  console.log('API URL:', process.env.REACT_APP_API_URL);
  console.log('Tenant ID:', process.env.REACT_APP_TENANT_ID);
  console.log('Environment:', process.env.NODE_ENV);
  
  // Check localStorage
  console.log('Auth tokens:', localStorage.getItem('unified_auth_tokens'));
  console.log('User info:', localStorage.getItem('unified_user_info'));
  
  // Test direct API call
  try {
    const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:3000/api/v1';
    console.log('Testing login to:', apiUrl + '/auth/login');
    
    const response = await fetch(apiUrl + '/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-tenant-id': process.env.REACT_APP_TENANT_ID || '28a833c0-c2a1-4498-85ca-b028f982ffb2'
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'admin123'
      })
    });
    
    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));
    
    const text = await response.text();
    console.log('Response body:', text);
    
    if (response.ok) {
      try {
        const data = JSON.parse(text);
        console.log('Login successful!', data);
      } catch (e) {
        console.error('Failed to parse response:', e);
      }
    }
  } catch (error) {
    console.error('Login test error:', error);
  }
  
  console.log('=== END DEBUG ===');
};

// Auto-run on page load for debugging
if (typeof window !== 'undefined') {
  (window as any).debugLogin = debugLogin;
}