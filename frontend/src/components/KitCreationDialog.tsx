import React, { useState } from 'react';
import {
  <PERSON>alog,
  <PERSON>alogT<PERSON>le,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Box,
  Typography,
  IconButton,
  Grid,
  Alert,
  Chip,
  Stack,
  InputAdornment,
  Card,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  FormControlLabel,
  Switch,
} from '@mui/material';
import {
  Add as AddIcon,
  Remove as RemoveIcon,
  Close as CloseIcon,
  LocalOffer as LocalOfferIcon,
  Inventory as InventoryIcon,
} from '@mui/icons-material';
import { VariationType, ProductVariation } from '../types/Product';

const variationTypeLabels: Record<VariationType, string> = {
  [VariationType.CAPSULAS]: 'Cápsulas',
  [VariationType.GOTAS]: 'Gotas',
  [VariationType.GEL]: 'Gel',
  [VariationType.SPRAY]: 'Spray',
  [VariationType.CREME]: 'Creme',
  [VariationType.CUSTOM]: 'Personalizado',
};

// Type imported from '../types/Product'

interface KitItem {
  variationId: string;
  quantity: number;
}

interface KitCreationDialogProps {
  open: boolean;
  onClose: () => void;
  onSave: (kit: {
    name: string;
    description?: string;
    price: number;
    items: KitItem[];
    isActive: boolean;
  }) => Promise<void>;
  productName: string;
  variations: ProductVariation[];
}

const KitCreationDialog: React.FC<KitCreationDialogProps> = ({
  open,
  onClose,
  onSave,
  productName,
  variations,
}) => {
  const [loading, setLoading] = useState(false);
  const [kitName, setKitName] = useState('');
  const [kitDescription, setKitDescription] = useState('');
  const [kitPrice, setKitPrice] = useState(0);
  const [active, setActive] = useState(true);
  const [kitItems, setKitItems] = useState<Record<string, number>>({});
  const [sku, setSku] = useState<string>('');
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Reset form when dialog opens/closes
  React.useEffect(() => {
    console.log('KitCreationDialog - open state changed:', open);
    if (open) {
      console.log('KitCreationDialog - resetting form');
      console.log('KitCreationDialog - variations available:', variations);
      setKitName('');
      setKitDescription('');
      setKitPrice(0);
      setActive(true);
      setKitItems({});
      setSku('KIT-' + Math.random().toString(36).substring(2, 10).toUpperCase());
      setErrors({});
    }
  }, [open, variations]);

  const handleQuantityChange = (variationId: string, delta: number) => {
    setKitItems(prev => {
      const currentQty = prev[variationId] || 0;
      const newQty = Math.max(0, currentQty + delta);
      
      if (newQty === 0) {
        const { [variationId]: removed, ...rest } = prev;
        return rest;
      }
      
      return { ...prev, [variationId]: newQty };
    });
  };

  const getTotalItems = () => {
    return Object.values(kitItems).reduce((sum, qty) => sum + qty, 0);
  };

  const getCostEstimate = () => {
    return Object.entries(kitItems).reduce((sum, [varId, qty]) => {
      const variation = variations.find(v => v.id === varId);
      return sum + (variation?.price || 0) * qty;
    }, 0);
  };

  const getKitSummary = () => {
    return Object.entries(kitItems)
      .filter(([_, qty]) => qty > 0)
      .map(([varId, qty]) => {
        const variation = variations.find(v => v.id === varId);
        const name = variation?.type === VariationType.CUSTOM 
          ? variation.customName 
          : variationTypeLabels[variation?.type || VariationType.GEL];
        return `${qty} ${name}`;
      })
      .join(', ');
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!kitName.trim()) {
      newErrors.name = 'Nome do kit é obrigatório';
    }

    if (kitPrice <= 0) {
      newErrors.price = 'Preço deve ser maior que zero';
    }

    if (Object.keys(kitItems).length === 0) {
      newErrors.items = 'Adicione pelo menos um item ao kit';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    console.log('KitCreationDialog - handleSave called');
    
    if (!validateForm()) {
      console.log('KitCreationDialog - validation failed');
      return;
    }

    setLoading(true);
    try {
      const items: KitItem[] = Object.entries(kitItems)
        .filter(([_, qty]) => qty > 0)
        .map(([variationId, quantity]) => ({
          variationId,
          quantity,
        }));

      const kitData = {
        name: kitName,
        description: kitDescription || undefined,
        price: kitPrice,
        items,
        isActive: active, // Backend expects isActive, not active
      };
      
      console.log('KitCreationDialog - saving kit with data:', kitData);
      
      await onSave(kitData);
      onClose();
    } catch (error) {
      console.error('Error saving kit:', error);
      // Don't close dialog on error
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Box display="flex" alignItems="center" justifyContent="space-between">
          <Box>
            <Typography variant="h6">Criar Kit</Typography>
            <Typography variant="body2" color="text.secondary">
              {productName}
            </Typography>
          </Box>
          <IconButton onClick={onClose} size="small">
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>
      
      <DialogContent dividers>
        <Grid container spacing={3}>
          {/* Kit Information */}
          <Grid item xs={12}>
            <Box display="flex" alignItems="center" gap={2} mb={2}>
              <Chip label={sku} color="primary" variant="outlined" />
              <Typography variant="caption" color="text.secondary">
                SKU gerado automaticamente
              </Typography>
            </Box>
          </Grid>

          <Grid item xs={12} md={8}>
            <TextField
              fullWidth
              label="Nome do Kit"
              value={kitName}
              onChange={(e) => setKitName(e.target.value)}
              error={!!errors.name}
              helperText={errors.name}
              placeholder="Ex: Tratamento 6 Meses"
              required
            />
          </Grid>

          <Grid item xs={12} md={4}>
            <TextField
              fullWidth
              label="Preço"
              type="number"
              value={kitPrice}
              onChange={(e) => setKitPrice(parseFloat(e.target.value) || 0)}
              error={!!errors.price}
              helperText={errors.price}
              InputProps={{
                startAdornment: <InputAdornment position="start">R$</InputAdornment>,
              }}
              required
            />
          </Grid>

          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Descrição"
              value={kitDescription}
              onChange={(e) => setKitDescription(e.target.value)}
              multiline
              rows={2}
              placeholder="Descrição opcional do kit"
            />
          </Grid>

          <Grid item xs={12}>
            <FormControlLabel
              control={
                <Switch
                  checked={active}
                  onChange={(e) => setActive(e.target.checked)}
                />
              }
              label="Kit Ativo (aparece para vendedores)"
            />
          </Grid>

          {/* Variations Selection */}
          <Grid item xs={12}>
            <Typography variant="subtitle1" gutterBottom fontWeight="bold">
              Composição do Kit
            </Typography>
            {errors.items && (
              <Alert severity="error" sx={{ mb: 2 }}>{errors.items}</Alert>
            )}
          </Grid>

          <Grid item xs={12}>
            <TableContainer component={Paper} variant="outlined">
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>Variação</TableCell>
                    <TableCell align="center">Quantidade</TableCell>
                    <TableCell align="right">Custo Unit.</TableCell>
                    <TableCell align="right">Custo Total</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {variations.map((variation) => {
                    const quantity = kitItems[variation.id] || 0;
                    const totalCost = quantity * variation.price;
                    
                    return (
                      <TableRow key={variation.id}>
                        <TableCell>
                          <Typography variant="body2">
                            {variation.variation || 
                             (variation.type === VariationType.CUSTOM 
                              ? variation.customName 
                              : variationTypeLabels[variation.type])}
                          </Typography>
                        </TableCell>
                        <TableCell align="center">
                          <Box display="flex" alignItems="center" justifyContent="center" gap={1}>
                            <IconButton
                              size="small"
                              onClick={() => handleQuantityChange(variation.id, -1)}
                              disabled={quantity === 0}
                            >
                              <RemoveIcon fontSize="small" />
                            </IconButton>
                            <Typography variant="body1" sx={{ minWidth: 30, textAlign: 'center' }}>
                              {quantity}
                            </Typography>
                            <IconButton
                              size="small"
                              onClick={() => handleQuantityChange(variation.id, 1)}
                            >
                              <AddIcon fontSize="small" />
                            </IconButton>
                          </Box>
                        </TableCell>
                        <TableCell align="right">
                          <Typography variant="body2">
                            R$ {variation.price.toFixed(2)}
                          </Typography>
                        </TableCell>
                        <TableCell align="right">
                          <Typography variant="body2" fontWeight={quantity > 0 ? 'bold' : 'normal'}>
                            R$ {totalCost.toFixed(2)}
                          </Typography>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </TableContainer>
          </Grid>

          {/* Summary */}
          {getTotalItems() > 0 && (
            <Grid item xs={12}>
              <Card variant="outlined">
                <CardContent>
                  <Typography variant="subtitle2" gutterBottom>
                    Resumo do Kit
                  </Typography>
                  <Stack spacing={1}>
                    <Box display="flex" justifyContent="space-between">
                      <Typography variant="body2" color="text.secondary">
                        Composição:
                      </Typography>
                      <Typography variant="body2">
                        {getKitSummary()}
                      </Typography>
                    </Box>
                    <Box display="flex" justifyContent="space-between">
                      <Typography variant="body2" color="text.secondary">
                        Custo estimado:
                      </Typography>
                      <Typography variant="body2">
                        R$ {getCostEstimate().toFixed(2)}
                      </Typography>
                    </Box>
                    <Box display="flex" justifyContent="space-between">
                      <Typography variant="body2" color="text.secondary">
                        Preço de venda:
                      </Typography>
                      <Typography variant="body2" fontWeight="bold">
                        R$ {kitPrice.toFixed(2)}
                      </Typography>
                    </Box>
                    {kitPrice > 0 && getCostEstimate() > 0 && (
                      <Box display="flex" justifyContent="space-between">
                        <Typography variant="body2" color="text.secondary">
                          Margem:
                        </Typography>
                        <Typography 
                          variant="body2" 
                          color={kitPrice > getCostEstimate() ? 'success.main' : 'error.main'}
                          fontWeight="bold"
                        >
                          {((kitPrice - getCostEstimate()) / kitPrice * 100).toFixed(1)}%
                        </Typography>
                      </Box>
                    )}
                  </Stack>
                </CardContent>
              </Card>
            </Grid>
          )}
        </Grid>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} disabled={loading}>
          Cancelar
        </Button>
        <Button
          onClick={handleSave}
          variant="contained"
          disabled={loading || !kitName.trim() || kitPrice <= 0 || getTotalItems() === 0}
          startIcon={<LocalOfferIcon />}
        >
          {loading ? 'Criando...' : 'Criar Kit'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default KitCreationDialog;