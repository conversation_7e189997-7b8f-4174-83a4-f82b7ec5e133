import React, { useState, useEffect } from 'react';
import {
  Box,
  Drawer,
  List,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Collapse,
  Typography,
  IconButton,
  Tooltip,
  Avatar,
  Divider,
  useTheme,
  alpha,
  Chip,
  ListItem,
} from '@mui/material';
import { Link, useLocation } from 'react-router-dom';
import { styled } from '@mui/material/styles';

// Icons
import DashboardRoundedIcon from '@mui/icons-material/DashboardRounded';
import ShoppingBagRoundedIcon from '@mui/icons-material/ShoppingBagRounded';
import InventoryRoundedIcon from '@mui/icons-material/InventoryRounded';
import ContentCopyRoundedIcon from '@mui/icons-material/ContentCopyRounded';
import ChevronLeftRoundedIcon from '@mui/icons-material/ChevronLeftRounded';
import ChevronRightRoundedIcon from '@mui/icons-material/ChevronRightRounded';
import LogoutRoundedIcon from '@mui/icons-material/LogoutRounded';
import SecurityRoundedIcon from '@mui/icons-material/SecurityRounded';
// Status icons
import ArticleOutlinedIcon from '@mui/icons-material/ArticleOutlined';
import PaidOutlinedIcon from '@mui/icons-material/PaidOutlined';
import CheckCircleOutlinedIcon from '@mui/icons-material/CheckCircleOutlined';
import CancelOutlinedIcon from '@mui/icons-material/CancelOutlined';
import HourglassEmptyOutlinedIcon from '@mui/icons-material/HourglassEmptyOutlined';
import ErrorOutlineOutlinedIcon from '@mui/icons-material/ErrorOutlineOutlined';
import ReplayOutlinedIcon from '@mui/icons-material/ReplayOutlined';
import HandshakeOutlinedIcon from '@mui/icons-material/HandshakeOutlined';
import LocalShippingOutlinedIcon from '@mui/icons-material/LocalShippingOutlined';
import WarningAmberOutlinedIcon from '@mui/icons-material/WarningAmberOutlined';
import ThumbUpAltOutlinedIcon from '@mui/icons-material/ThumbUpAltOutlined';
import KeyboardReturnOutlinedIcon from '@mui/icons-material/KeyboardReturnOutlined';
import AccessTimeOutlinedIcon from '@mui/icons-material/AccessTimeOutlined';
import CalendarTodayOutlinedIcon from '@mui/icons-material/CalendarTodayOutlined';
import ContentCopyOutlinedIcon from '@mui/icons-material/ContentCopyOutlined';
import DeleteIcon from '@mui/icons-material/Delete';

import UnifiedAuthService from '../services/UnifiedAuthService';
import { Order } from '../types/Order';
import { StatusFilter } from './Sidebar';

interface ModernSidebarProps {
  orders?: Order[];
  onStatusSelect?: (filter: StatusFilter | null) => void;
  selectedStatus?: StatusFilter | null;
  open?: boolean;
  onToggle?: (open: boolean) => void;
}

const drawerWidth = 260;
const drawerWidthCollapsed = 72;

const StyledDrawer = styled(Drawer, {
  shouldForwardProp: (prop) => prop !== 'open',
})(({ theme, open }) => ({
  width: open ? drawerWidth : drawerWidthCollapsed,
  flexShrink: 0,
  whiteSpace: 'nowrap',
  boxSizing: 'border-box',
  [theme.breakpoints.down('sm')]: {
    width: 0,
  },
  '& .MuiDrawer-paper': {
    width: open ? drawerWidth : drawerWidthCollapsed,
    transition: theme.transitions.create('width', {
      easing: theme.transitions.easing.sharp,
      duration: theme.transitions.duration.enteringScreen,
    }),
    overflowX: 'hidden',
    overflowY: 'auto',
    backgroundColor: theme.palette.mode === 'dark' ? '#0F1420' : '#ffffff',
    color: theme.palette.mode === 'dark' ? '#E4E4E7' : '#000000',
    borderRight: theme.palette.mode === 'dark' ? '1px solid rgba(255, 255, 255, 0.12)' : '1px solid rgba(0, 0, 0, 0.08)',
    boxShadow: theme.palette.mode === 'dark' ? '4px 0 24px rgba(0, 0, 0, 0.24)' : '2px 0 8px rgba(0, 0, 0, 0.08)',
    marginTop: '64px',
    height: 'calc(100vh - 64px)',
    position: 'relative',
    [theme.breakpoints.down('sm')]: {
      position: 'fixed',
      width: open ? '80vw' : 0,
      maxWidth: drawerWidth,
      zIndex: theme.zIndex.drawer,
    },
  },
}));

const StyledListItemButton = styled(ListItemButton)(({ theme }) => ({
  margin: '4px 8px',
  borderRadius: '8px',
  transition: 'all 0.2s ease',
  '&:hover': {
    backgroundColor: theme.palette.mode === 'dark' ? alpha('#2196F3', 0.08) : 'rgba(0, 0, 0, 0.04)',
  },
  '&.Mui-selected': {
    backgroundColor: theme.palette.mode === 'dark' ? alpha('#2196F3', 0.16) : 'rgba(33, 150, 243, 0.08)',
    color: '#2196F3',
    '&:hover': {
      backgroundColor: theme.palette.mode === 'dark' ? alpha('#2196F3', 0.24) : 'rgba(33, 150, 243, 0.12)',
    },
    '& .MuiListItemIcon-root': {
      color: '#2196F3',
    },
    '& .MuiListItemText-primary': {
      color: '#2196F3',
      fontWeight: 600,
    },
  },
}));

const StyledListItemIcon = styled(ListItemIcon)(({ theme }) => ({
  minWidth: 45,
  color: theme.palette.mode === 'dark' ? '#A1A1AA' : 'inherit',
}));

const SectionTitle = styled(Typography)(({ theme }) => ({
  padding: '20px 24px 8px',
  fontSize: '0.75rem',
  fontWeight: 600,
  letterSpacing: '0.1em',
  textTransform: 'uppercase',
  color: theme.palette.mode === 'dark' ? '#71717A' : '#64748B',
}));

const ModernSidebar: React.FC<ModernSidebarProps> = ({ orders = [], onStatusSelect, selectedStatus, open: propOpen, onToggle }) => {
  const theme = useTheme();
  const location = useLocation();
  const [localOpen, setLocalOpen] = useState(true);
  const open = propOpen !== undefined ? propOpen : localOpen;
  const [userInfo, setUserInfo] = useState<any>(null);
  const [currentUserEmail, setCurrentUserEmail] = useState('');
  const [currentUserName, setCurrentUserName] = useState('');
  const [statusUpdateTrigger, setStatusUpdateTrigger] = useState(0);

  const isSeller = userInfo?.role === 'seller';
  const isAdmin = userInfo?.role === 'admin';
  const isSupervisor = userInfo?.role === 'supervisor';
  const isOperator = userInfo?.role === 'collector';

  useEffect(() => {
    const info = UnifiedAuthService.getUserInfo();
    setUserInfo(info);
    if (info) {
      setCurrentUserEmail(info.email);
      setCurrentUserName(info.fullName);
    }

    // Listen for order status updates
    const handleOrderStatusUpdate = (event: CustomEvent) => {
      setStatusUpdateTrigger(prev => prev + 1);
    };

    window.addEventListener('order-status-updated', handleOrderStatusUpdate as EventListener);

    return () => {
      window.removeEventListener('order-status-updated', handleOrderStatusUpdate as EventListener);
    };
  }, []);

  const handleDrawerToggle = () => {
    const newState = !open;
    if (propOpen === undefined) {
      setLocalOpen(newState);
    }
    if (onToggle) {
      onToggle(newState);
    }
  };

  // Filter orders by user role
  const filterOrdersByUserRole = (ordersToFilter: Order[]): Order[] => {
    if (isAdmin || isSupervisor) {
      return ordersToFilter;
    }

    if (isSeller) {
      return ordersToFilter.filter(order => {
        if (!order.vendedor) return false;
        const vendedorNormalizado = order.vendedor.toLowerCase().trim();
        const userNameNormalizado = currentUserName ? currentUserName.toLowerCase().trim() : '';
        const userEmailNormalizado = currentUserEmail ? currentUserEmail.toLowerCase().trim() : '';
        return vendedorNormalizado.includes(userNameNormalizado) ||
               userNameNormalizado.includes(vendedorNormalizado) ||
               vendedorNormalizado.includes(userEmailNormalizado) ||
               userEmailNormalizado.includes(vendedorNormalizado);
      });
    }

    if (isOperator) {
      return ordersToFilter.filter(order => {
        if (!order.operador) return false;
        const operadorNormalizado = order.operador.toLowerCase().trim();
        const userNameNormalizado = currentUserName ? currentUserName.toLowerCase().trim() : '';
        const userEmailNormalizado = currentUserEmail ? currentUserEmail.toLowerCase().trim() : '';
        return operadorNormalizado.includes(userNameNormalizado) ||
               userNameNormalizado.includes(operadorNormalizado) ||
               operadorNormalizado.includes(userEmailNormalizado) ||
               userEmailNormalizado.includes(operadorNormalizado);
      });
    }

    return ordersToFilter;
  };

  const filteredOrdersByRole = filterOrdersByUserRole(orders);

  const getStatusCount = (status: string): number => {
    return filteredOrdersByRole.filter(order =>
      typeof order.situacao === 'string' &&
      order.situacao.toLowerCase() === status.toLowerCase()
    ).length;
  };

  const getTotalOrders = () => {
    return filteredOrdersByRole.filter(order =>
      !!order.idVenda &&
      (!order.situacao || order.situacao.toLowerCase() !== 'deletado')
    ).length;
  };

  const getPendingPayments = () => {
    return filteredOrdersByRole.filter(order =>
      typeof order.situacao === 'string' &&
      order.situacao.toLowerCase() === 'pagamento pendente'
    ).length;
  };

  const getCompletedOrders = () => {
    return filteredOrdersByRole.filter(order =>
      typeof order.situacao === 'string' &&
      order.situacao.toLowerCase() === 'completo'
    ).length;
  };

  const getCanceledOrders = () => {
    return filteredOrdersByRole.filter(order =>
      typeof order.situacao === 'string' &&
      order.situacao.toLowerCase() === 'cancelado'
    ).length;
  };

  const getDeletedOrders = () => {
    return filteredOrdersByRole.filter(order =>
      typeof order.situacao === 'string' &&
      order.situacao.toLowerCase() === 'deletado'
    ).length;
  };

  const getReceiveToday = () => {
    const today = new Date().toLocaleDateString('pt-BR');
    return filteredOrdersByRole.filter(order => order.dataRecebimento === today).length;
  };

  const statusItems = [
    { text: 'Todos os Pedidos', icon: <ArticleOutlinedIcon />, count: getTotalOrders(), filter: null },
    { text: 'Pagamento Pendente', icon: <PaidOutlinedIcon sx={{ color: '#F5A623' }} />, count: getPendingPayments(), filter: { field: 'situacao', value: 'pagamento pendente' } },
    { text: 'Completo', icon: <CheckCircleOutlinedIcon sx={{ color: '#4CAF50' }} />, count: getCompletedOrders(), filter: { field: 'situacao', value: 'completo' } },
    { text: 'Cancelado', icon: <CancelOutlinedIcon sx={{ color: '#F44336' }} />, count: getCanceledOrders(), filter: { field: 'situacao', value: 'cancelado' } },
    { text: 'Em Separação', icon: <HourglassEmptyOutlinedIcon sx={{ color: '#2196F3' }} />, count: getStatusCount('Em Separação'), filter: { field: 'situacao', value: 'em separação' } },
    { text: 'Frustrado', icon: <ErrorOutlineOutlinedIcon sx={{ color: '#F44336' }} />, count: getStatusCount('Frustrado'), filter: { field: 'situacao', value: 'frustrado' } },
    { text: 'Recuperação', icon: <ReplayOutlinedIcon sx={{ color: '#673AB7' }} />, count: getStatusCount('Recuperação'), filter: { field: 'situacao', value: 'recuperação' } },
    { text: 'Negociação', icon: <HandshakeOutlinedIcon sx={{ color: '#3F51B5' }} />, count: getStatusCount('Negociação'), filter: { field: 'situacao', value: 'negociação' } },
    { text: 'Retirar Correios', icon: <LocalShippingOutlinedIcon sx={{ color: '#F5A623' }} />, count: getStatusCount('Retirar Correios'), filter: { field: 'situacao', value: 'retirar correios' } },
    { text: 'Entrega Falha', icon: <WarningAmberOutlinedIcon sx={{ color: '#F44336' }} />, count: getStatusCount('Entrega Falha'), filter: { field: 'situacao', value: 'entrega falha' } },
    { text: 'Confirmar Entrega', icon: <ThumbUpAltOutlinedIcon sx={{ color: '#4CAF50' }} />, count: getStatusCount('Confirmar Entrega'), filter: { field: 'situacao', value: 'confirmar entrega' } },
    { text: 'Devolvido Correios', icon: <KeyboardReturnOutlinedIcon sx={{ color: '#F44336' }} />, count: getStatusCount('Devolvido Correios'), filter: { field: 'situacao', value: 'devolvido correios' } },
    { text: 'Liberação', icon: <AccessTimeOutlinedIcon sx={{ color: '#2196F3' }} />, count: getStatusCount('liberação'), filter: { field: 'situacao', value: 'liberação' } },
    { text: 'Receber Hoje', icon: <CalendarTodayOutlinedIcon sx={{ color: '#2196F3' }} />, count: getReceiveToday(), filter: { field: 'special', value: 'dataRecebimento' } },
    { text: 'Possíveis Duplicados', icon: <ContentCopyOutlinedIcon sx={{ color: '#FF9800' }} />, count: getStatusCount('Possíveis Duplicados'), filter: { field: 'situacao', value: 'possíveis duplicados' } },
  ];

  const adminStatusItems = [
    { text: 'Deletado', icon: <DeleteIcon sx={{ color: '#F44336' }} />, count: getDeletedOrders(), filter: { field: 'situacao', value: 'deletado' } },
  ];

  const isFilterEqual = (a: StatusFilter | null, b: StatusFilter | null) => {
    if (a === null && b === null) return true;
    if (a === null || b === null) return false;
    return a.field === b.field && a.value === b.value;
  };

  const handleStatusClick = (filter: StatusFilter | null) => {
    if (onStatusSelect) {
      onStatusSelect(filter);
    }
    const event = new CustomEvent('update-pedidos-filter', { detail: filter });
    window.dispatchEvent(event);
  };

  const isActive = (path: string) => location.pathname === path;

  const handleLogout = () => {
    UnifiedAuthService.logout();
  };

  const listItemStyle = {
    borderRadius: '8px',
    mb: 0.5,
    py: 1,
    px: 1.5,
    '&.Mui-selected': {
      bgcolor: 'rgba(33, 150, 243, 0.08)',
      color: '#2196F3',
      '& .MuiListItemIcon-root': {
        color: '#2196F3',
      },
      '&:hover': {
        bgcolor: 'rgba(33, 150, 243, 0.12)',
      }
    },
    '&:hover': {
      bgcolor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.04)' : 'rgba(0, 0, 0, 0.04)'
    }
  };

  return (
    <StyledDrawer variant="permanent" open={open}>
      <Box sx={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
        {/* Toggle Button */}
        <Box sx={{ display: 'flex', justifyContent: 'flex-end', p: 1 }}>
          <IconButton
            onClick={handleDrawerToggle}
            sx={{
              color: theme.palette.mode === 'dark' ? '#A1A1AA' : 'text.secondary',
              '&:hover': {
                backgroundColor: alpha(theme.palette.primary.main, 0.08),
              },
            }}
          >
            {open ? <ChevronLeftRoundedIcon /> : <ChevronRightRoundedIcon />}
          </IconButton>
        </Box>

        {/* Main Navigation */}
        <List sx={{ pt: 2, pl: 2, pr: 0.5 }}>
          <ListItem
            button
            component={Link}
            to="/dashboard"
            selected={location.pathname === '/dashboard'}
            sx={listItemStyle}
          >
            <ListItemIcon>
              <DashboardRoundedIcon sx={{ color: location.pathname === '/dashboard' ? '#2196F3' : undefined }} />
            </ListItemIcon>
            {open && <ListItemText primary="Dashboard" />}
          </ListItem>

          <ListItem
            button
            component={Link}
            to="/dashboard/pedidos"
            selected={location.pathname === '/dashboard/pedidos'}
            sx={listItemStyle}
          >
            <ListItemIcon>
              <ShoppingBagRoundedIcon sx={{ color: location.pathname === '/dashboard/pedidos' ? '#2196F3' : '#FF9800' }} />
            </ListItemIcon>
            {open && <ListItemText primary="Pedidos" />}
          </ListItem>

          <ListItem
            button
            component={Link}
            to="/dashboard/produtos"
            selected={location.pathname === '/dashboard/produtos'}
            sx={listItemStyle}
          >
            <ListItemIcon>
              <InventoryRoundedIcon sx={{ color: location.pathname === '/dashboard/produtos' ? '#2196F3' : '#9C27B0' }} />
            </ListItemIcon>
            {open && <ListItemText primary="Produtos" />}
          </ListItem>

          {(isAdmin || isSupervisor) && (
            <>
              <ListItem
                button
                component={Link}
                to="/dashboard/duplicates"
                selected={location.pathname === '/dashboard/duplicates'}
                sx={listItemStyle}
              >
                <ListItemIcon>
                  <ContentCopyRoundedIcon sx={{ color: location.pathname === '/dashboard/duplicates' ? '#2196F3' : '#FF9800' }} />
                </ListItemIcon>
                {open && <ListItemText primary="Pedidos Duplicados" />}
              </ListItem>

              <ListItem
                button
                component={Link}
                to="/dashboard/antifraud"
                selected={location.pathname === '/dashboard/antifraud'}
                sx={listItemStyle}
              >
                <ListItemIcon>
                  <SecurityRoundedIcon sx={{ color: location.pathname === '/dashboard/antifraud' ? '#2196F3' : '#F44336' }} />
                </ListItemIcon>
                {open && <ListItemText primary="Anti-Fraude" />}
              </ListItem>
            </>
          )}
        </List>

        {/* Status Filters for Pedidos page */}
        {location.pathname === '/dashboard/pedidos' && open && (
          <>
            <Divider sx={{ my: 2 }} />
            <Box sx={{ pl: 1.5, pr: 1, pb: 2 }}>
              <SectionTitle>Filtros de Status</SectionTitle>
              <List sx={{ p: 0 }}>
                {statusItems.map((item, index) => (
                  <ListItem
                    button
                    key={index}
                    selected={!!(selectedStatus && isFilterEqual(selectedStatus, item.filter))}
                    onClick={() => handleStatusClick(item.filter)}
                    sx={{
                      py: 1,
                      borderRadius: '8px',
                      mb: 0.5,
                      '&.Mui-selected': {
                        bgcolor: 'rgba(33, 150, 243, 0.1)',
                        '&:hover': {
                          bgcolor: 'rgba(33, 150, 243, 0.12)'
                        }
                      },
                      '&:hover': {
                        bgcolor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.02)' : 'rgba(0, 0, 0, 0.02)'
                      }
                    }}
                  >
                    <ListItemIcon sx={{ minWidth: 45 }}>
                      {item.icon}
                    </ListItemIcon>
                    <ListItemText
                      primary={item.text}
                      primaryTypographyProps={{ fontSize: '0.875rem', fontWeight: 500 }}
                    />
                    <Chip
                      label={item.count}
                      size="small"
                      sx={{
                        height: 20,
                        fontSize: '0.70rem',
                        bgcolor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.04)' : 'rgba(0, 0, 0, 0.04)',
                        color: theme.palette.mode === 'dark' ? '#A1A1AA' : '#64748B',
                        fontWeight: 600,
                      }}
                    />
                  </ListItem>
                ))}

                {/* Admin-only deleted status */}
                {isAdmin && adminStatusItems.map((item, index) => (
                  <ListItem
                    button
                    key={`admin-${index}`}
                    selected={!!(selectedStatus && isFilterEqual(selectedStatus, item.filter))}
                    onClick={() => handleStatusClick(item.filter)}
                    sx={{
                      py: 1,
                      mt: 1,
                      borderTop: '1px dashed',
                      borderColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.12)' : 'rgba(0, 0, 0, 0.12)',
                      borderTopLeftRadius: 0,
                      borderTopRightRadius: 0
                    }}
                  >
                    <ListItemIcon sx={{ minWidth: 45 }}>
                      {item.icon}
                    </ListItemIcon>
                    <ListItemText
                      primary={item.text}
                      primaryTypographyProps={{
                        fontSize: '0.875rem',
                        fontWeight: 600,
                        color: '#D32F2F'
                      }}
                    />
                    <Chip
                      label={item.count}
                      size="small"
                      sx={{
                        height: 20,
                        fontSize: '0.70rem',
                        bgcolor: 'rgba(211, 47, 47, 0.1)',
                        color: '#D32F2F',
                        fontWeight: 600,
                      }}
                    />
                  </ListItem>
                ))}
              </List>
            </Box>
          </>
        )}

        {/* Spacer */}
        <Box sx={{ flexGrow: 1 }} />

        {/* User Profile (at bottom) */}
        {open && userInfo && (
          <>
            <Divider sx={{ borderColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.06)' : 'rgba(0, 0, 0, 0.06)', mx: 2 }} />
            <Box sx={{ px: 3, py: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Avatar
                  sx={{
                    width: 40,
                    height: 40,
                    bgcolor: theme.palette.primary.main,
                    fontSize: '1rem',
                  }}
                >
                  {userInfo.fullName?.charAt(0).toUpperCase()}
                </Avatar>
                <Box sx={{ overflow: 'hidden' }}>
                  <Typography
                    variant="subtitle2"
                    sx={{
                      fontWeight: 600,
                      whiteSpace: 'nowrap',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                    }}
                  >
                    {userInfo.fullName}
                  </Typography>
                  <Typography
                    variant="caption"
                    sx={{
                      color: theme.palette.mode === 'dark' ? '#A1A1AA' : 'text.secondary',
                      whiteSpace: 'nowrap',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                    }}
                  >
                    {userInfo.role === 'admin' ? 'Administrador' :
                     userInfo.role === 'supervisor' ? 'Supervisor' :
                     userInfo.role === 'collector' ? 'Operador' :
                     userInfo.role === 'seller' ? 'Vendedor' : userInfo.role}
                  </Typography>
                </Box>
              </Box>
            </Box>
          </>
        )}

        {/* Logout Button */}
        <Box sx={{ p: 2 }}>
          <StyledListItemButton
            onClick={handleLogout}
            sx={{
              color: '#EF4444',
              '&:hover': {
                backgroundColor: alpha('#EF4444', 0.08),
              },
            }}
          >
            <Tooltip title={!open ? 'Sair' : ''} placement="right">
              <StyledListItemIcon sx={{ color: '#EF4444' }}>
                <LogoutRoundedIcon />
              </StyledListItemIcon>
            </Tooltip>
            {open && (
              <ListItemText
                primary="Sair"
                primaryTypographyProps={{
                  fontSize: '0.875rem',
                  fontWeight: 500,
                }}
              />
            )}
          </StyledListItemButton>
        </Box>
      </Box>
    </StyledDrawer>
  );
};

export default ModernSidebar;
export { drawerWidth, drawerWidthCollapsed };