import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import OrderComparisonView from '../OrderComparisonView';
import AntifraudService from '../../../services/AntifraudService';
import { useAntifraudStore } from '../../../stores/antifraudStore';

// Mock dependencies
jest.mock('../../../services/AntifraudService');
jest.mock('../../../stores/antifraudStore');

const createQueryWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );
};

describe('OrderComparisonView', () => {
  const mockLeftOrder = {
    id: '1',
    orderNumber: 'ORD-001',
    customerName: 'John Doe',
    customerCPF: '123.456.789-00',
    customerEmail: '<EMAIL>',
    customerPhone: '(11) 98765-4321',
    fullAddress: 'Rua das Flores, 123, Apt 45, Jardim Paulista, São Paulo - SP, 01234-567',
    totalAmount: 150.00,
    paymentMethod: 'COD',
    status: 'PENDING',
    duplicateMatchScore: 85,
    matchedComponents: ['street', 'number', 'neighborhood'],
    createdAt: '2024-01-01T10:00:00Z',
    updatedAt: '2024-01-01T10:30:00Z',
  };

  const mockRightOrder = {
    id: '2',
    orderNumber: 'ORD-002',
    customerName: 'João Doe',
    customerCPF: '123.456.789-00',
    customerEmail: '<EMAIL>',
    customerPhone: '(11) 98765-4322',
    fullAddress: 'R. das Flores, 123, Apto 45, Jd. Paulista, São Paulo - SP, 01234-567',
    totalAmount: 180.00,
    paymentMethod: 'COD',
    status: 'PENDING',
    duplicateMatchScore: 85,
    matchedComponents: ['street', 'number', 'neighborhood'],
    createdAt: '2024-01-01T14:00:00Z',
    updatedAt: '2024-01-01T14:30:00Z',
  };

  const mockAuditTrail = [
    {
      id: '1',
      action: 'ORDER_CREATED',
      performedBy: 'System',
      timestamp: '2024-01-01T10:00:00Z',
      details: { source: 'API' },
    },
    {
      id: '2',
      action: 'DUPLICATE_DETECTED',
      performedBy: 'Anti-fraud System',
      timestamp: '2024-01-01T10:00:05Z',
      details: { matchScore: 85, matchedWith: 'ORD-002' },
    },
  ];

  const mockReviewOrder = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    (AntifraudService.getOrderById as jest.Mock).mockResolvedValue(mockRightOrder);
    (AntifraudService.getOrderAuditTrail as jest.Mock).mockResolvedValue(mockAuditTrail);
    (useAntifraudStore as unknown as jest.Mock).mockReturnValue({
      reviewOrder: mockReviewOrder,
    });
  });

  describe('Rendering', () => {
    it('should render both orders side by side', async () => {
      render(
        <OrderComparisonView
          leftOrder={mockLeftOrder}
          rightOrderId="2"
          onClose={jest.fn()}
        />,
        { wrapper: createQueryWrapper() }
      );

      // Wait for right order to load
      await waitFor(() => {
        expect(screen.getByText('ORD-001')).toBeInTheDocument();
        expect(screen.getByText('ORD-002')).toBeInTheDocument();
      });

      // Check customer names
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('João Doe')).toBeInTheDocument();
    });

    it('should show loading state for right order', () => {
      (AntifraudService.getOrderById as jest.Mock).mockImplementation(
        () => new Promise(() => {}) // Never resolves
      );

      render(
        <OrderComparisonView
          leftOrder={mockLeftOrder}
          rightOrderId="2"
          onClose={jest.fn()}
        />,
        { wrapper: createQueryWrapper() }
      );

      expect(screen.getAllByRole('progressbar')).toHaveLength(1);
    });

    it('should show error state if right order fails to load', async () => {
      (AntifraudService.getOrderById as jest.Mock).mockRejectedValue(
        new Error('Failed to load order')
      );

      render(
        <OrderComparisonView
          leftOrder={mockLeftOrder}
          rightOrderId="2"
          onClose={jest.fn()}
        />,
        { wrapper: createQueryWrapper() }
      );

      await waitFor(() => {
        expect(screen.getByText(/Erro ao carregar pedido/i)).toBeInTheDocument();
      });
    });
  });

  describe('Tabs Navigation', () => {
    it('should switch between tabs', async () => {
      const user = userEvent.setup();
      render(
        <OrderComparisonView
          leftOrder={mockLeftOrder}
          rightOrderId="2"
          onClose={jest.fn()}
        />,
        { wrapper: createQueryWrapper() }
      );

      await waitFor(() => {
        expect(screen.getByText('ORD-002')).toBeInTheDocument();
      });

      // Initially on General tab
      expect(screen.getByText('Informações Gerais')).toBeInTheDocument();

      // Switch to Addresses tab
      const addressTab = screen.getByRole('tab', { name: /Endereços/i });
      await user.click(addressTab);

      expect(screen.getByText(/Componentes do Endereço/i)).toBeInTheDocument();

      // Switch to History tab
      const historyTab = screen.getByRole('tab', { name: /Histórico/i });
      await user.click(historyTab);

      expect(screen.getByText(/Auditoria do Pedido/i)).toBeInTheDocument();
    });
  });

  describe('Diff Highlighting', () => {
    it('should highlight differences in general info', async () => {
      render(
        <OrderComparisonView
          leftOrder={mockLeftOrder}
          rightOrderId="2"
          onClose={jest.fn()}
        />,
        { wrapper: createQueryWrapper() }
      );

      await waitFor(() => {
        expect(screen.getByText('ORD-002')).toBeInTheDocument();
      });

      // Check for highlighted differences
      const highlightedElements = screen.getAllByTestId('diff-highlight');
      expect(highlightedElements.length).toBeGreaterThan(0);
    });

    it('should show identical CPF without highlighting', async () => {
      render(
        <OrderComparisonView
          leftOrder={mockLeftOrder}
          rightOrderId="2"
          onClose={jest.fn()}
        />,
        { wrapper: createQueryWrapper() }
      );

      await waitFor(() => {
        expect(screen.getByText('ORD-002')).toBeInTheDocument();
      });

      // CPF should not be highlighted as it's the same
      const cpfElements = screen.getAllByText('123.456.789-00');
      cpfElements.forEach(element => {
        expect(element.closest('[data-testid="diff-highlight"]')).not.toBeInTheDocument();
      });
    });
  });

  describe('Match Score Visualization', () => {
    it('should display match score and breakdown', async () => {
      render(
        <OrderComparisonView
          leftOrder={mockLeftOrder}
          rightOrderId="2"
          onClose={jest.fn()}
        />,
        { wrapper: createQueryWrapper() }
      );

      await waitFor(() => {
        expect(screen.getByText('ORD-002')).toBeInTheDocument();
      });

      // Check match score
      expect(screen.getByText('85%')).toBeInTheDocument();
      expect(screen.getByText(/Alta Similaridade/i)).toBeInTheDocument();

      // Check matched components
      expect(screen.getByText(/street, number, neighborhood/i)).toBeInTheDocument();
    });
  });

  describe('Swap Functionality', () => {
    it('should swap orders when swap button is clicked', async () => {
      const user = userEvent.setup();
      const { rerender } = render(
        <OrderComparisonView
          leftOrder={mockLeftOrder}
          rightOrderId="2"
          onClose={jest.fn()}
        />,
        { wrapper: createQueryWrapper() }
      );

      await waitFor(() => {
        expect(screen.getByText('ORD-002')).toBeInTheDocument();
      });

      const swapButton = screen.getByLabelText(/Trocar pedidos/i);
      await user.click(swapButton);

      // Component should re-render with swapped orders
      rerender(
        <OrderComparisonView
          leftOrder={mockRightOrder}
          rightOrderId="1"
          onClose={jest.fn()}
        />
      );

      // Verify orders are swapped
      const leftTitle = screen.getAllByText(/Pedido/)[0].parentElement;
      const rightTitle = screen.getAllByText(/Pedido/)[1].parentElement;

      expect(leftTitle).toHaveTextContent('ORD-002');
      expect(rightTitle).toHaveTextContent('ORD-001');
    });
  });

  describe('Review Actions', () => {
    it('should approve both orders', async () => {
      const user = userEvent.setup();
      render(
        <OrderComparisonView
          leftOrder={mockLeftOrder}
          rightOrderId="2"
          onClose={jest.fn()}
        />,
        { wrapper: createQueryWrapper() }
      );

      await waitFor(() => {
        expect(screen.getByText('ORD-002')).toBeInTheDocument();
      });

      const approveButton = screen.getByRole('button', { name: /Aprovar Ambos/i });
      await user.click(approveButton);

      // Confirm in dialog
      const confirmButton = screen.getByRole('button', { name: /Confirmar/i });
      await user.click(confirmButton);

      expect(mockReviewOrder).toHaveBeenCalledTimes(2);
      expect(mockReviewOrder).toHaveBeenCalledWith('1', 'APPROVED', '');
      expect(mockReviewOrder).toHaveBeenCalledWith('2', 'APPROVED', '');
    });

    it('should deny both orders with reason', async () => {
      const user = userEvent.setup();
      render(
        <OrderComparisonView
          leftOrder={mockLeftOrder}
          rightOrderId="2"
          onClose={jest.fn()}
        />,
        { wrapper: createQueryWrapper() }
      );

      await waitFor(() => {
        expect(screen.getByText('ORD-002')).toBeInTheDocument();
      });

      const denyButton = screen.getByRole('button', { name: /Negar Ambos/i });
      await user.click(denyButton);

      // Add reason
      const reasonInput = screen.getByLabelText(/Motivo/i);
      await user.type(reasonInput, 'Duplicate orders confirmed');

      // Confirm
      const confirmButton = screen.getByRole('button', { name: /Confirmar/i });
      await user.click(confirmButton);

      expect(mockReviewOrder).toHaveBeenCalledTimes(2);
      expect(mockReviewOrder).toHaveBeenCalledWith('1', 'DENIED', 'Duplicate orders confirmed');
      expect(mockReviewOrder).toHaveBeenCalledWith('2', 'DENIED', 'Duplicate orders confirmed');
    });

    it('should approve one and deny other', async () => {
      const user = userEvent.setup();
      render(
        <OrderComparisonView
          leftOrder={mockLeftOrder}
          rightOrderId="2"
          onClose={jest.fn()}
        />,
        { wrapper: createQueryWrapper() }
      );

      await waitFor(() => {
        expect(screen.getByText('ORD-002')).toBeInTheDocument();
      });

      const approveOneButton = screen.getByRole('button', { name: /Aprovar ORD-001/i });
      await user.click(approveOneButton);

      // Confirm
      const confirmButton = screen.getByRole('button', { name: /Confirmar/i });
      await user.click(confirmButton);

      expect(mockReviewOrder).toHaveBeenCalledTimes(2);
      expect(mockReviewOrder).toHaveBeenCalledWith('1', 'APPROVED', '');
      expect(mockReviewOrder).toHaveBeenCalledWith('2', 'DENIED', expect.any(String));
    });
  });

  describe('Address Comparison', () => {
    it('should show address components breakdown', async () => {
      const user = userEvent.setup();
      render(
        <OrderComparisonView
          leftOrder={mockLeftOrder}
          rightOrderId="2"
          onClose={jest.fn()}
        />,
        { wrapper: createQueryWrapper() }
      );

      await waitFor(() => {
        expect(screen.getByText('ORD-002')).toBeInTheDocument();
      });

      // Switch to Addresses tab
      const addressTab = screen.getByRole('tab', { name: /Endereços/i });
      await user.click(addressTab);

      // Check address components
      expect(screen.getByText('Rua das Flores')).toBeInTheDocument();
      expect(screen.getByText('R. das Flores')).toBeInTheDocument();
      expect(screen.getByText('123')).toBeInTheDocument();
      expect(screen.getByText('Jardim Paulista')).toBeInTheDocument();
      expect(screen.getByText('Jd. Paulista')).toBeInTheDocument();
    });
  });

  describe('Audit Trail', () => {
    it('should display audit trail for both orders', async () => {
      const user = userEvent.setup();
      render(
        <OrderComparisonView
          leftOrder={mockLeftOrder}
          rightOrderId="2"
          onClose={jest.fn()}
        />,
        { wrapper: createQueryWrapper() }
      );

      await waitFor(() => {
        expect(screen.getByText('ORD-002')).toBeInTheDocument();
      });

      // Switch to History tab
      const historyTab = screen.getByRole('tab', { name: /Histórico/i });
      await user.click(historyTab);

      // Check audit entries
      expect(screen.getByText('ORDER_CREATED')).toBeInTheDocument();
      expect(screen.getByText('DUPLICATE_DETECTED')).toBeInTheDocument();
      expect(screen.getByText('Anti-fraud System')).toBeInTheDocument();
    });
  });

  describe('Close Functionality', () => {
    it('should call onClose when close button is clicked', async () => {
      const user = userEvent.setup();
      const onClose = jest.fn();
      
      render(
        <OrderComparisonView
          leftOrder={mockLeftOrder}
          rightOrderId="2"
          onClose={onClose}
        />,
        { wrapper: createQueryWrapper() }
      );

      await waitFor(() => {
        expect(screen.getByText('ORD-002')).toBeInTheDocument();
      });

      const closeButton = screen.getByLabelText(/Fechar comparação/i);
      await user.click(closeButton);

      expect(onClose).toHaveBeenCalled();
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA labels and roles', async () => {
      render(
        <OrderComparisonView
          leftOrder={mockLeftOrder}
          rightOrderId="2"
          onClose={jest.fn()}
        />,
        { wrapper: createQueryWrapper() }
      );

      await waitFor(() => {
        expect(screen.getByText('ORD-002')).toBeInTheDocument();
      });

      expect(screen.getByRole('tablist')).toBeInTheDocument();
      expect(screen.getAllByRole('tab')).toHaveLength(4);
      expect(screen.getByRole('tabpanel')).toBeInTheDocument();
    });

    it('should support keyboard navigation between tabs', async () => {
      const user = userEvent.setup();
      render(
        <OrderComparisonView
          leftOrder={mockLeftOrder}
          rightOrderId="2"
          onClose={jest.fn()}
        />,
        { wrapper: createQueryWrapper() }
      );

      await waitFor(() => {
        expect(screen.getByText('ORD-002')).toBeInTheDocument();
      });

      // Focus first tab
      const firstTab = screen.getByRole('tab', { name: /Geral/i });
      firstTab.focus();

      // Navigate with arrow keys
      await user.keyboard('{ArrowRight}');
      expect(screen.getByRole('tab', { name: /Endereços/i })).toHaveFocus();

      await user.keyboard('{ArrowRight}');
      expect(screen.getByRole('tab', { name: /Histórico/i })).toHaveFocus();
    });
  });
});