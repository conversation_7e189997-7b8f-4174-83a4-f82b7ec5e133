import React, { useEffect, useMemo, useCallback, useState } from 'react';
import { FixedSizeList as List } from 'react-window';
import {
  Box,
  Paper,
  Typography,
  IconButton,
  Checkbox,
  Chip,
  TextField,
  InputAdornment,
  Button,
  Menu,
  MenuItem,
  Slider,
  FormControl,
  InputLabel,
  Select,
  Tooltip,
  LinearProgress,
} from '@mui/material';
import {
  Search as SearchIcon,
  FilterList as FilterIcon,
  Sort as SortIcon,
  Refresh as RefreshIcon,
  CheckCircle as ApproveIcon,
  Cancel as DenyIcon,
  GetApp as ExportIcon,
  KeyboardArrowDown as ArrowDownIcon,
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { useAntifraudStore } from '../../stores/antifraudStore';
import { useNotificationContext } from '../../contexts/NotificationContext';
import AntifraudService from '../../services/AntifraudService';

const ROW_HEIGHT = 80;
const HEADER_HEIGHT = 60;

const EnhancedReviewQueue: React.FC = () => {
  const {
    queue,
    filters,
    sorting,
    selection,
    loading,
    error,
    page,
    totalPages,
    setFilters,
    setSorting,
    toggleSelection,
    selectAll,
    clearSelection,
    fetchQueue,
    approveOrderOptimistic,
    confirmOptimisticUpdate,
    revertOptimisticUpdate,
    getFilteredQueue,
    getSortedQueue,
  } = useAntifraudStore();

  const { showNotification } = useNotificationContext();
  const [filterMenuAnchor, setFilterMenuAnchor] = useState<null | HTMLElement>(null);
  const [sortMenuAnchor, setSortMenuAnchor] = useState<null | HTMLElement>(null);
  const [bulkMenuAnchor, setBulkMenuAnchor] = useState<null | HTMLElement>(null);
  const [containerHeight, setContainerHeight] = useState(600);

  // Fetch data on mount
  useEffect(() => {
    fetchQueue();
  }, [fetchQueue, page]);

  // Calculate container height
  useEffect(() => {
    const updateHeight = () => {
      const height = window.innerHeight - 300; // Account for header and padding
      setContainerHeight(Math.max(400, height));
    };
    
    updateHeight();
    window.addEventListener('resize', updateHeight);
    return () => window.removeEventListener('resize', updateHeight);
  }, []);

  const sortedQueue = useMemo(() => getSortedQueue(), [getSortedQueue, queue, filters, sorting]);
  const hasSelection = selection.size > 0;

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (e.ctrlKey || e.metaKey) {
        switch (e.key) {
          case 'a':
            e.preventDefault();
            selectAll();
            break;
          case 'r':
            e.preventDefault();
            fetchQueue();
            break;
          case 'f':
            e.preventDefault();
            document.getElementById('search-input')?.focus();
            break;
        }
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [selectAll, fetchQueue]);

  const handleApprove = useCallback(async (orderId: string) => {
    try {
      approveOrderOptimistic(orderId);
      
      await AntifraudService.reviewDuplicate(orderId, {
        decision: 'APPROVE_ORDER',
        notes: 'Aprovado via ação rápida',
      });
      
      confirmOptimisticUpdate(orderId);
      showNotification('Pedido aprovado com sucesso', 'success');
    } catch (error) {
      revertOptimisticUpdate(orderId);
      showNotification('Erro ao aprovar pedido', 'error');
    }
  }, [approveOrderOptimistic, confirmOptimisticUpdate, revertOptimisticUpdate, showNotification]);

  const handleBulkAction = useCallback(async (action: 'approve' | 'deny') => {
    const selectedItems = sortedQueue.filter(item => selection.has(item.id));
    const decision = action === 'approve' ? 'APPROVE_ORDER' : 'DENY_ORDER';
    
    showNotification(`Processando ${selectedItems.length} pedidos...`, 'info');
    
    // Process in batches of 5
    for (let i = 0; i < selectedItems.length; i += 5) {
      const batch = selectedItems.slice(i, i + 5);
      
      await Promise.all(
        batch.map(item =>
          AntifraudService.reviewDuplicate(item.id, {
            decision,
            notes: `Ação em lote - ${action === 'approve' ? 'Aprovado' : 'Negado'}`,
          })
        )
      );
    }
    
    clearSelection();
    fetchQueue();
    showNotification(`${selectedItems.length} pedidos processados`, 'success');
  }, [selection, sortedQueue, clearSelection, fetchQueue, showNotification]);

  const handleExport = useCallback(() => {
    const data = sortedQueue.map(item => ({
      'Pedido': item.orderNumber,
      'Cliente': item.customerName,
      'Telefone': item.customerPhone,
      'Valor': item.total,
      'Similaridade': `${item.duplicateMatchScore}%`,
      'Data': format(new Date(item.createdAt), 'dd/MM/yyyy HH:mm'),
      'Pedidos Similares': item.matchedOrders.length,
    }));
    
    const csv = [
      Object.keys(data[0]).join(','),
      ...data.map(row => Object.values(row).join(','))
    ].join('\n');
    
    const blob = new Blob([csv], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `duplicatas_${format(new Date(), 'yyyyMMdd_HHmmss')}.csv`;
    a.click();
    
    showNotification('Dados exportados com sucesso', 'success');
  }, [sortedQueue, showNotification]);

  // Virtual row renderer
  const Row = ({ index, style }: { index: number; style: React.CSSProperties }) => {
    const item = sortedQueue[index];
    if (!item) return null;
    
    const isSelected = selection.has(item.id);
    
    return (
      <Box
        style={style}
        sx={{
          display: 'flex',
          alignItems: 'center',
          px: 2,
          borderBottom: '1px solid',
          borderColor: 'divider',
          bgcolor: isSelected ? 'action.selected' : 'background.paper',
          '&:hover': {
            bgcolor: isSelected ? 'action.selected' : 'action.hover',
          },
        }}
      >
        <Checkbox
          checked={isSelected}
          onChange={() => toggleSelection(item.id)}
          size="small"
        />
        
        <Box flex={1} display="flex" alignItems="center" gap={2}>
          <Box minWidth={120}>
            <Typography variant="body2" fontWeight="bold">
              {item.orderNumber}
            </Typography>
            <Typography variant="caption" color="textSecondary">
              {format(new Date(item.createdAt), 'dd/MM HH:mm')}
            </Typography>
          </Box>
          
          <Box flex={1}>
            <Typography variant="body2">{item.customerName}</Typography>
            <Typography variant="caption" color="textSecondary">
              {item.customerPhone}
            </Typography>
          </Box>
          
          <Box minWidth={100} textAlign="right">
            <Typography variant="body2">
              {new Intl.NumberFormat('pt-BR', {
                style: 'currency',
                currency: 'BRL',
              }).format(item.total)}
            </Typography>
          </Box>
          
          <Box minWidth={100} textAlign="center">
            <Chip
              label={`${item.duplicateMatchScore}%`}
              size="small"
              style={{
                backgroundColor: AntifraudService.getMatchScoreColor(item.duplicateMatchScore),
                color: 'white',
              }}
            />
          </Box>
          
          <Box minWidth={80} textAlign="center">
            <Chip
              label={`${item.matchedOrders.length} similar`}
              size="small"
              variant="outlined"
            />
          </Box>
          
          <Box minWidth={120} display="flex" gap={1}>
            <Tooltip title="Aprovar (A)">
              <IconButton
                size="small"
                color="success"
                onClick={() => handleApprove(item.id)}
              >
                <ApproveIcon />
              </IconButton>
            </Tooltip>
            <Tooltip title="Negar (D)">
              <IconButton
                size="small"
                color="error"
                onClick={() => {/* Open deny dialog */}}
              >
                <DenyIcon />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>
      </Box>
    );
  };

  return (
    <Paper elevation={2}>
      {/* Header */}
      <Box
        sx={{
          p: 2,
          borderBottom: 1,
          borderColor: 'divider',
          display: 'flex',
          alignItems: 'center',
          gap: 2,
        }}
      >
        <Typography variant="h6" component="h2" flex={1}>
          Fila de Revisão ({sortedQueue.length} {sortedQueue.length === 1 ? 'item' : 'itens'})
        </Typography>
        
        {/* Search */}
        <TextField
          id="search-input"
          size="small"
          placeholder="Buscar..."
          value={filters.searchTerm}
          onChange={(e) => setFilters({ searchTerm: e.target.value })}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
          sx={{ width: 250 }}
        />
        
        {/* Filter Menu */}
        <Tooltip title="Filtros (Ctrl+F)">
          <IconButton onClick={(e) => setFilterMenuAnchor(e.currentTarget)}>
            <FilterIcon />
          </IconButton>
        </Tooltip>
        
        {/* Sort Menu */}
        <Tooltip title="Ordenar">
          <IconButton onClick={(e) => setSortMenuAnchor(e.currentTarget)}>
            <SortIcon />
          </IconButton>
        </Tooltip>
        
        {/* Refresh */}
        <Tooltip title="Atualizar (Ctrl+R)">
          <IconButton onClick={() => fetchQueue()}>
            <RefreshIcon />
          </IconButton>
        </Tooltip>
        
        {/* Export */}
        <Tooltip title="Exportar CSV">
          <IconButton onClick={handleExport}>
            <ExportIcon />
          </IconButton>
        </Tooltip>
        
        {/* Bulk Actions */}
        {hasSelection && (
          <Button
            variant="contained"
            size="small"
            endIcon={<ArrowDownIcon />}
            onClick={(e) => setBulkMenuAnchor(e.currentTarget)}
          >
            {selection.size} selecionado(s)
          </Button>
        )}
      </Box>
      
      {/* Loading indicator */}
      {loading && <LinearProgress />}
      
      {/* Virtual list */}
      <List
        height={containerHeight}
        itemCount={sortedQueue.length}
        itemSize={ROW_HEIGHT}
        width="100%"
      >
        {Row}
      </List>
      
      {/* Filter Menu */}
      <Menu
        anchorEl={filterMenuAnchor}
        open={Boolean(filterMenuAnchor)}
        onClose={() => setFilterMenuAnchor(null)}
      >
        <Box sx={{ p: 2, width: 300 }}>
          <Typography variant="subtitle2" gutterBottom>
            Similaridade
          </Typography>
          <Slider
            value={filters.scoreRange}
            onChange={(_, value) => setFilters({ scoreRange: value as [number, number] })}
            valueLabelDisplay="auto"
            min={0}
            max={100}
            marks={[
              { value: 0, label: '0%' },
              { value: 50, label: '50%' },
              { value: 100, label: '100%' },
            ]}
          />
          
          <Typography variant="subtitle2" gutterBottom sx={{ mt: 2 }}>
            Período
          </Typography>
          <Box display="flex" gap={1}>
            <DatePicker
              label="De"
              value={filters.dateRange[0]}
              onChange={(date) => setFilters({ 
                dateRange: [date, filters.dateRange[1]] 
              })}
              slotProps={{ textField: { size: 'small' } }}
            />
            <DatePicker
              label="Até"
              value={filters.dateRange[1]}
              onChange={(date) => setFilters({ 
                dateRange: [filters.dateRange[0], date] 
              })}
              slotProps={{ textField: { size: 'small' } }}
            />
          </Box>
          
          <Button
            fullWidth
            sx={{ mt: 2 }}
            onClick={() => {
              setFilters({
                scoreRange: [0, 100],
                dateRange: [null, null],
                searchTerm: '',
              });
              setFilterMenuAnchor(null);
            }}
          >
            Limpar Filtros
          </Button>
        </Box>
      </Menu>
      
      {/* Sort Menu */}
      <Menu
        anchorEl={sortMenuAnchor}
        open={Boolean(sortMenuAnchor)}
        onClose={() => setSortMenuAnchor(null)}
      >
        {[
          { column: 'createdAt', label: 'Data' },
          { column: 'matchScore', label: 'Similaridade' },
          { column: 'total', label: 'Valor' },
          { column: 'customerName', label: 'Cliente' },
        ].map(({ column, label }) => (
          <MenuItem
            key={column}
            onClick={() => {
              setSorting({
                column: column as any,
                direction: sorting.column === column && sorting.direction === 'asc' ? 'desc' : 'asc',
              });
              setSortMenuAnchor(null);
            }}
          >
            {label}
            {sorting.column === column && (
              <Box component="span" ml={1}>
                {sorting.direction === 'asc' ? '↑' : '↓'}
              </Box>
            )}
          </MenuItem>
        ))}
      </Menu>
      
      {/* Bulk Actions Menu */}
      <Menu
        anchorEl={bulkMenuAnchor}
        open={Boolean(bulkMenuAnchor)}
        onClose={() => setBulkMenuAnchor(null)}
      >
        <MenuItem onClick={() => {
          handleBulkAction('approve');
          setBulkMenuAnchor(null);
        }}>
          <ApproveIcon sx={{ mr: 1 }} color="success" />
          Aprovar Selecionados
        </MenuItem>
        <MenuItem onClick={() => {
          handleBulkAction('deny');
          setBulkMenuAnchor(null);
        }}>
          <DenyIcon sx={{ mr: 1 }} color="error" />
          Negar Selecionados
        </MenuItem>
      </Menu>
    </Paper>
  );
};

export default EnhancedReviewQueue;