import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>Title,
  DialogContent,
  <PERSON>alog<PERSON>ctions,
  Button,
  TextField,
  Box,
  Typography,
  Divider,
  IconButton,
  Grid,
  Alert,
  Chip,
  Stack,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Card,
  CardContent,
  Avatar,
  Tooltip,
  FormControlLabel,
  Checkbox,
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Image as ImageIcon,
  AttachMoney as AttachMoneyIcon,
  Close as CloseIcon,
  CheckCircle as CheckCircleIcon,
} from '@mui/icons-material';
import { VariationType, CreateVariationDto, generateSku } from '../types/Product';

const variationTypeLabels: Record<VariationType, string> = {
  [VariationType.CAPSULAS]: 'Cápsulas',
  [VariationType.GOTAS]: 'Gotas',
  [VariationType.GEL]: 'Gel',
  [VariationType.SPRAY]: 'Spray',
  [VariationType.CREME]: 'Creme',
  [VariationType.CUSTOM]: 'Personalizado',
};

// Use CreateVariationDto for variations being created
type ProductVariation = CreateVariationDto & {
  type: VariationType;
  customName?: string;
};

interface ProductCreationV2DialogProps {
  open: boolean;
  onClose: () => void;
  onSave: (product: {
    name: string;
    description?: string;
    imageUrl?: string;
    variations: ProductVariation[];
  }) => Promise<void>;
}

const ProductCreationV2Dialog: React.FC<ProductCreationV2DialogProps> = ({
  open,
  onClose,
  onSave,
}) => {
  const [loading, setLoading] = useState(false);
  const [productName, setProductName] = useState('');
  const [productDescription, setProductDescription] = useState('');
  const [productImageUrl, setProductImageUrl] = useState('');
  const [variations, setVariations] = useState<ProductVariation[]>([]);
  const [selectedTypes, setSelectedTypes] = useState<Set<VariationType>>(new Set());
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Reset form when dialog opens/closes
  React.useEffect(() => {
    if (open) {
      setProductName('');
      setProductDescription('');
      setProductImageUrl('');
      setVariations([]);
      setSelectedTypes(new Set());
      setErrors({});
    }
  }, [open]);

  const handleAddVariation = (type: VariationType) => {
    console.log('Clicked variation:', type); // Debug log
    
    if (selectedTypes.has(type) && type !== VariationType.CUSTOM) {
      // Remove variation
      setSelectedTypes(prev => {
        const newSet = new Set(prev);
        newSet.delete(type);
        return newSet;
      });
      setVariations(prev => prev.filter(v => v.type !== type));
    } else {
      // Add variation
      setSelectedTypes(prev => new Set(prev).add(type));
      setVariations(prev => [...prev, { 
        type, 
        price: 0, 
        variation: variationTypeLabels[type], 
        sku: generateSku(productName || 'PROD', variationTypeLabels[type]) 
      }]);
    }
    
    // Clear variation error when user selects a variation
    if (errors.variations) {
      setErrors(prev => ({ ...prev, variations: '' }));
    }
  };

  const handleUpdateVariation = (index: number, updates: Partial<ProductVariation>) => {
    setVariations(prev => prev.map((v, i) => {
      if (i !== index) return v;
      const updated = { ...v, ...updates };
      // If custom name is updated, also update the variation field
      if (updates.customName && v.type === VariationType.CUSTOM) {
        updated.variation = updates.customName;
      }
      return updated;
    }));
  };

  const handleRemoveVariation = (index: number) => {
    const variation = variations[index];
    setVariations(prev => prev.filter((_, i) => i !== index));
    if (variation.type !== VariationType.CUSTOM) {
      setSelectedTypes(prev => {
        const newSet = new Set(prev);
        newSet.delete(variation.type);
        return newSet;
      });
    }
  };

  const validateForm = (): boolean => {
    console.log('validateForm called');
    const newErrors: Record<string, string> = {};

    if (!productName.trim()) {
      newErrors.name = 'Nome do produto é obrigatório';
    }

    if (variations.length === 0) {
      newErrors.variations = 'Adicione pelo menos uma variação ao produto';
    }

    // Validate custom variations have names
    variations.forEach((v, index) => {
      if (v.type === VariationType.CUSTOM && !v.customName?.trim()) {
        newErrors[`custom-${index}`] = 'Nome da variação personalizada é obrigatório';
      }
      if (v.price < 0) {
        newErrors[`cost-${index}`] = 'Preço de custo não pode ser negativo';
      }
    });

    console.log('Validation errors:', newErrors);
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    console.log('handleSave called');
    console.log('productName:', productName);
    console.log('variations:', variations);
    
    if (!validateForm()) {
      console.log('Validation failed');
      return;
    }

    console.log('Validation passed, saving...');
    setLoading(true);
    try {
      // Transform variations to remove the 'type' field before sending
      const transformedVariations = variations.map(v => {
        // Get the display name for the variation
        const variationName = v.type === VariationType.CUSTOM 
          ? (v.customName || 'Custom') 
          : variationTypeLabels[v.type];
          
        // Return ONLY the fields expected by backend
        return {
          variation: variationName,
          sku: v.sku || generateSku(productName, variationName),
          price: Number(v.price) || 0, // Ensure it's a number
        };
      });
      
      console.log('Transformed variations:', transformedVariations);
      
      // Build payload with ONLY the fields expected by backend
      const payload: any = {
        name: productName.trim(),
        variations: transformedVariations,
      };
      
      // Only add optional fields if they have values
      if (productDescription && productDescription.trim()) {
        payload.description = productDescription.trim();
      }
      
      if (productImageUrl && productImageUrl.trim()) {
        payload.imageUrl = productImageUrl.trim();
      }
      
      console.log('🔍 ProductCreationV2Dialog - Final payload to send:', JSON.stringify(payload, null, 2));
      
      await onSave(payload);
      console.log('Save successful');
      onClose();
    } catch (error: any) {
      console.error('Error saving product:', error);
      
      // Extract error details
      let errorMessage = 'Erro ao salvar produto: ';
      
      if (error.response?.data?.message) {
        if (Array.isArray(error.response.data.message)) {
          errorMessage += error.response.data.message.join(', ');
        } else {
          errorMessage += error.response.data.message;
        }
      } else if (error.message) {
        errorMessage += error.message;
      } else {
        errorMessage += 'Erro desconhecido';
      }
      
      alert(errorMessage);
      
      // Log details for debugging
      console.error('Full error details:', {
        status: error.response?.status,
        data: error.response?.data,
        config: error.config
      });
    } finally {
      setLoading(false);
    }
  };

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // In a real implementation, you would upload to a server
      // For now, we'll create a local URL
      const reader = new FileReader();
      reader.onloadend = () => {
        setProductImageUrl(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Box display="flex" alignItems="center" justifyContent="space-between">
          <Typography variant="h6">Novo Produto</Typography>
          <IconButton onClick={onClose} size="small">
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>
      
      <DialogContent dividers>
        <Grid container spacing={3}>
          {/* Product Information */}
          <Grid item xs={12}>
            <Typography variant="subtitle1" gutterBottom fontWeight="bold">
              Informações do Produto
            </Typography>
          </Grid>

          <Grid item xs={12} md={8}>
            <TextField
              fullWidth
              label="Nome do Produto"
              value={productName}
              onChange={(e) => {
                const newName = e.target.value;
                setProductName(newName);
                // Update SKUs for all variations when product name changes
                if (newName.trim()) {
                  setVariations(prev => prev.map(v => ({
                    ...v,
                    sku: generateSku(newName, v.variation)
                  })));
                }
              }}
              error={!!errors.name}
              helperText={errors.name}
              required
            />
          </Grid>

          <Grid item xs={12} md={4}>
            <Box display="flex" alignItems="center" gap={2}>
              <Avatar
                src={productImageUrl}
                sx={{ width: 60, height: 60 }}
              >
                <ImageIcon />
              </Avatar>
              <Button
                variant="outlined"
                component="label"
                startIcon={<ImageIcon />}
              >
                Upload Imagem
                <input
                  type="file"
                  hidden
                  accept="image/*"
                  onChange={handleImageUpload}
                />
              </Button>
            </Box>
          </Grid>

          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Descrição"
              value={productDescription}
              onChange={(e) => setProductDescription(e.target.value)}
              multiline
              rows={2}
            />
          </Grid>

          {/* Variations Section */}
          <Grid item xs={12}>
            <Divider sx={{ my: 2 }} />
            <Typography variant="subtitle1" gutterBottom fontWeight="bold">
              Variações do Produto
            </Typography>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              Clique nos tipos de variação que este produto possui (pelo menos um é obrigatório)
            </Typography>
          </Grid>

          <Grid item xs={12}>
            <Stack direction="row" spacing={1} flexWrap="wrap" gap={1}>
              {Object.values(VariationType).map((type) => (
                <Chip
                  key={type}
                  label={variationTypeLabels[type]}
                  onClick={() => handleAddVariation(type)}
                  color={selectedTypes.has(type) && type !== VariationType.CUSTOM ? "primary" : "default"}
                  variant={selectedTypes.has(type) && type !== VariationType.CUSTOM ? "filled" : "outlined"}
                  icon={selectedTypes.has(type) && type !== VariationType.CUSTOM ? <CheckCircleIcon /> : undefined}
                  sx={{ 
                    cursor: 'pointer',
                    '&:hover': {
                      transform: 'scale(1.05)',
                      boxShadow: 2,
                    },
                    transition: 'all 0.2s ease'
                  }}
                />
              ))}
            </Stack>
          </Grid>

          {errors.variations && (
            <Grid item xs={12}>
              <Alert severity="error">{errors.variations}</Alert>
            </Grid>
          )}

          {/* Variation Details */}
          {variations.length > 0 && (
            <Grid item xs={12}>
              <Typography variant="subtitle2" gutterBottom sx={{ mt: 2 }}>
                Configurar Variações
              </Typography>
              <Stack spacing={2}>
                {variations.map((variation, index) => (
                  <Card key={index} variant="outlined">
                    <CardContent>
                      <Grid container spacing={2} alignItems="center">
                        <Grid item xs={12} md={4}>
                          {variation.type === VariationType.CUSTOM ? (
                            <TextField
                              fullWidth
                              label="Nome da Variação"
                              value={variation.customName || ''}
                              onChange={(e) => handleUpdateVariation(index, { customName: e.target.value })}
                              error={!!errors[`custom-${index}`]}
                              helperText={errors[`custom-${index}`]}
                              required
                            />
                          ) : (
                            <Box display="flex" alignItems="center" gap={1}>
                              <Chip label={variationTypeLabels[variation.type]} color="primary" />
                            </Box>
                          )}
                        </Grid>
                        <Grid item xs={12} md={6}>
                          <TextField
                            fullWidth
                            label="Preço de Custo"
                            type="number"
                            value={variation.price}
                            onChange={(e) => handleUpdateVariation(index, { price: parseFloat(e.target.value) || 0 })}
                            InputProps={{
                              startAdornment: <InputAdornment position="start">R$</InputAdornment>,
                            }}
                            error={!!errors[`cost-${index}`]}
                            helperText={errors[`cost-${index}`]}
                          />
                        </Grid>
                        <Grid item xs={12} md={2}>
                          <IconButton
                            color="error"
                            onClick={() => handleRemoveVariation(index)}
                          >
                            <DeleteIcon />
                          </IconButton>
                        </Grid>
                      </Grid>
                    </CardContent>
                  </Card>
                ))}
              </Stack>
            </Grid>
          )}

          {/* Help text when no variations selected */}
          {variations.length === 0 && (
            <Grid item xs={12}>
              <Alert severity="warning">
                <Typography variant="body2">
                  Selecione pelo menos uma variação acima para continuar.
                </Typography>
              </Alert>
            </Grid>
          )}

          {/* Summary */}
          {variations.length > 0 && (
            <Grid item xs={12}>
              <Alert severity="info" sx={{ mt: 2 }}>
                <Typography variant="body2">
                  Após criar o produto, você poderá criar kits combinando estas variações.
                </Typography>
              </Alert>
            </Grid>
          )}
        </Grid>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} disabled={loading}>
          Cancelar
        </Button>
        <Tooltip 
          title={
            !productName.trim() ? "Digite o nome do produto" :
            variations.length === 0 ? "Selecione pelo menos uma variação" :
            ""
          }
        >
          <span>
            <Button
              onClick={() => {
                console.log('Button clicked directly');
                handleSave();
              }}
              variant="contained"
              disabled={loading || !productName.trim() || variations.length === 0}
            >
              {loading ? 'Criando...' : 'Criar Produto'}
            </Button>
          </span>
        </Tooltip>
      </DialogActions>
    </Dialog>
  );
};

export default ProductCreationV2Dialog;