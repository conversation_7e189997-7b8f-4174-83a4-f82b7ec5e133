import React, { useState, useEffect } from 'react';
import {
  <PERSON>alog,
  <PERSON>alogTitle,
  DialogContent,
  <PERSON>alogActions,
  Button,
  TextField,
  Box,
  Typography,
  IconButton,
  Grid,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Autocomplete,
  InputAdornment,
  Divider,
  Chip,
} from '@mui/material';
import {
  Close as CloseIcon,
  Add as AddIcon,
  Remove as RemoveIcon,
  Delete as DeleteIcon,
  ShoppingCart as ShoppingCartIcon,
  Person as PersonIcon,
  Phone as PhoneIcon,
} from '@mui/icons-material';
import { productService } from '../services/ProductService';
import { kitService } from '../services/KitService';
import { VariationType } from '../types/Product';

interface DialogOrderItem {
  kitId: string;
  kitName: string;
  productName: string;
  quantity: number;
  unitPrice: number;
}

interface OrderCreationDialogProps {
  open: boolean;
  onClose: () => void;
  onSave: (order: {
    customerName: string;
    customerPhone: string;
    items: Array<{
      productId: string;
      productName: string;
      quantity: number;
      unitPrice: number;
    }>;
    customerId?: string;
    collectorId?: string;
    zapId?: string;
  }) => Promise<void>;
}

const OrderCreationDialog: React.FC<OrderCreationDialogProps> = ({
  open,
  onClose,
  onSave,
}) => {
  const [loading, setLoading] = useState(false);
  const [customerName, setCustomerName] = useState('');
  const [customerPhone, setCustomerPhone] = useState('');
  const [items, setItems] = useState<DialogOrderItem[]>([]);
  const [errors, setErrors] = useState<Record<string, string>>({});
  
  // Product and kit data
  const [products, setProducts] = useState<any[]>([]);
  const [kits, setKits] = useState<any[]>([]);
  const [selectedProduct, setSelectedProduct] = useState<any>(null);
  const [selectedKit, setSelectedKit] = useState<any>(null);
  const [quantity, setQuantity] = useState(1);
  const [loadingKits, setLoadingKits] = useState(false);

  // Load products when dialog opens
  useEffect(() => {
    if (open) {
      loadProducts();
      // Reset form
      setCustomerName('');
      setCustomerPhone('');
      setItems([]);
      setSelectedProduct(null);
      setSelectedKit(null);
      setKits([]);
      setQuantity(1);
      setErrors({});
    }
  }, [open]);

  // Load kits when product is selected
  useEffect(() => {
    if (selectedProduct) {
      loadKitsForProduct(selectedProduct.id);
    } else {
      setKits([]);
      setSelectedKit(null);
    }
  }, [selectedProduct]);

  const loadProducts = async () => {
    try {
      const productsData = await productService.getActiveProducts();
      setProducts(productsData);
    } catch (error) {
      console.error('Error loading products:', error);
    }
  };

  const loadKitsForProduct = async (productId: string) => {
    try {
      setLoadingKits(true);
      const productKits = await kitService.getKitsByProductId(productId);
      // Filter only active kits
      const activeKits = productKits.filter(kit => kit.active);
      setKits(activeKits);
    } catch (error) {
      console.error('Error loading kits:', error);
    } finally {
      setLoadingKits(false);
    }
  };

  const formatPhone = (value: string) => {
    // Remove non-numeric characters
    const numbers = value.replace(/\D/g, '');
    
    // Format as Brazilian phone number
    if (numbers.length <= 11) {
      if (numbers.length <= 2) return numbers;
      if (numbers.length <= 6) return `(${numbers.slice(0, 2)}) ${numbers.slice(2)}`;
      if (numbers.length <= 10) return `(${numbers.slice(0, 2)}) ${numbers.slice(2, 6)}-${numbers.slice(6)}`;
      return `(${numbers.slice(0, 2)}) ${numbers.slice(2, 7)}-${numbers.slice(7, 11)}`;
    }
    return value;
  };

  const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formatted = formatPhone(e.target.value);
    setCustomerPhone(formatted);
  };

  const addItem = () => {
    if (!selectedProduct || !selectedKit) return;

    const newItem: DialogOrderItem = {
      kitId: selectedKit.id,
      kitName: selectedKit.name,
      productName: selectedProduct.name,
      quantity,
      unitPrice: selectedKit.price || 0,
    };

    setItems([...items, newItem]);
    setSelectedProduct(null);
    setSelectedKit(null);
    setQuantity(1);
  };

  const getKitComposition = (kit: any) => {
    if (!kit.items) return '';
    return kit.items.map((item: any) => {
      const variation = item.productVariation;
      if (!variation) return '';
      return `${item.quantity} ${variation.variation || ''}`;
    }).join(', ');
  };

  const removeItem = (index: number) => {
    setItems(items.filter((_, i) => i !== index));
  };

  const updateItemQuantity = (index: number, newQuantity: number) => {
    if (newQuantity < 1) return;
    const newItems = [...items];
    newItems[index].quantity = newQuantity;
    setItems(newItems);
  };

  const getTotalAmount = () => {
    return items.reduce((sum, item) => sum + (item.unitPrice * item.quantity), 0);
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!customerName.trim()) {
      newErrors.name = 'Nome do cliente é obrigatório';
    }

    if (!customerPhone.trim()) {
      newErrors.phone = 'Telefone é obrigatório';
    } else if (customerPhone.replace(/\D/g, '').length < 10) {
      newErrors.phone = 'Telefone inválido';
    }

    if (items.length === 0) {
      newErrors.items = 'Adicione pelo menos um item ao pedido';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!validateForm()) return;

    setLoading(true);
    try {
      await onSave({
        customerName,
        customerPhone: customerPhone.replace(/\D/g, ''), // Send only numbers
        items: items.map(item => ({
          productId: item.kitId,
          productName: `${item.productName} - ${item.kitName}`,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
        })),
      });
      onClose();
    } catch (error) {
      console.error('Error creating order:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Box display="flex" alignItems="center" justifyContent="space-between">
          <Typography variant="h6">Criar Novo Pedido</Typography>
          <IconButton onClick={onClose} size="small">
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>
      
      <DialogContent dividers>
        <Grid container spacing={3}>
          {/* Customer Information */}
          <Grid item xs={12}>
            <Typography variant="subtitle1" gutterBottom fontWeight="bold">
              Informações do Cliente
            </Typography>
          </Grid>

          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Nome do Cliente"
              value={customerName}
              onChange={(e) => setCustomerName(e.target.value)}
              error={!!errors.name}
              helperText={errors.name}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <PersonIcon />
                  </InputAdornment>
                ),
              }}
              required
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Telefone"
              value={customerPhone}
              onChange={handlePhoneChange}
              error={!!errors.phone}
              helperText={errors.phone}
              placeholder="(11) 99999-9999"
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <PhoneIcon />
                  </InputAdornment>
                ),
              }}
              required
            />
          </Grid>

          <Grid item xs={12}>
            <Divider sx={{ my: 2 }} />
          </Grid>

          {/* Product Selection */}
          <Grid item xs={12}>
            <Typography variant="subtitle1" gutterBottom fontWeight="bold">
              Adicionar Produtos
            </Typography>
            {errors.items && (
              <Alert severity="error" sx={{ mb: 2 }}>{errors.items}</Alert>
            )}
          </Grid>

          <Grid item xs={12} md={4}>
            <Autocomplete
              value={selectedProduct}
              onChange={(_, newValue) => {
                setSelectedProduct(newValue);
                setSelectedKit(null);
              }}
              options={products}
              getOptionLabel={(option) => option.name}
              renderInput={(params) => (
                <TextField {...params} label="Produto" size="small" />
              )}
            />
          </Grid>

          {selectedProduct && (
            <Grid item xs={12} md={4}>
              <Autocomplete
                value={selectedKit}
                onChange={(_, newValue) => setSelectedKit(newValue)}
                options={kits}
                loading={loadingKits}
                getOptionLabel={(option) => option.name}
                renderOption={(props, option) => (
                  <Box component="li" {...props}>
                    <Box>
                      <Typography variant="body2">{option.name}</Typography>
                      <Typography variant="caption" color="text.secondary">
                        {getKitComposition(option)} - R$ {option.price?.toFixed(2) || '0.00'}
                      </Typography>
                    </Box>
                  </Box>
                )}
                renderInput={(params) => (
                  <TextField 
                    {...params} 
                    label="Kit" 
                    size="small" 
                    required
                    helperText={loadingKits ? "Carregando kits..." : kits.length === 0 ? "Nenhum kit ativo para este produto" : ""}
                  />
                )}
                disabled={!selectedProduct || loadingKits}
              />
            </Grid>
          )}

          <Grid item xs={12} md={2}>
            <TextField
              fullWidth
              label="Quantidade"
              type="number"
              value={quantity}
              onChange={(e) => setQuantity(Math.max(1, parseInt(e.target.value) || 1))}
              size="small"
              InputProps={{ inputProps: { min: 1 } }}
            />
          </Grid>

          <Grid item xs={12} md={2}>
            <Button
              fullWidth
              variant="contained"
              startIcon={<AddIcon />}
              onClick={addItem}
              disabled={!selectedProduct || !selectedKit}
            >
              Adicionar
            </Button>
          </Grid>

          {/* Items List */}
          {items.length > 0 && (
            <Grid item xs={12}>
              <TableContainer component={Paper} variant="outlined">
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>Produto</TableCell>
                      <TableCell align="center">Quantidade</TableCell>
                      <TableCell align="right">Preço Unit.</TableCell>
                      <TableCell align="right">Total</TableCell>
                      <TableCell align="center">Ações</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {items.map((item, index) => (
                      <TableRow key={index}>
                        <TableCell>
                          <Box>
                            <Typography variant="body2">{item.productName}</Typography>
                            <Typography variant="caption" color="text.secondary">
                              {item.kitName}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell align="center">
                          <Box display="flex" alignItems="center" justifyContent="center" gap={1}>
                            <IconButton
                              size="small"
                              onClick={() => updateItemQuantity(index, item.quantity - 1)}
                              disabled={item.quantity <= 1}
                            >
                              <RemoveIcon fontSize="small" />
                            </IconButton>
                            <Typography>{item.quantity}</Typography>
                            <IconButton
                              size="small"
                              onClick={() => updateItemQuantity(index, item.quantity + 1)}
                            >
                              <AddIcon fontSize="small" />
                            </IconButton>
                          </Box>
                        </TableCell>
                        <TableCell align="right">
                          R$ {item.unitPrice.toFixed(2)}
                        </TableCell>
                        <TableCell align="right">
                          R$ {(item.unitPrice * item.quantity).toFixed(2)}
                        </TableCell>
                        <TableCell align="center">
                          <IconButton
                            size="small"
                            onClick={() => removeItem(index)}
                            color="error"
                          >
                            <DeleteIcon fontSize="small" />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>

              <Box sx={{ mt: 2, textAlign: 'right' }}>
                <Typography variant="h6">
                  Total: R$ {getTotalAmount().toFixed(2)}
                </Typography>
              </Box>
            </Grid>
          )}
        </Grid>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} disabled={loading}>
          Cancelar
        </Button>
        <Button
          onClick={handleSave}
          variant="contained"
          disabled={loading || items.length === 0}
          startIcon={<ShoppingCartIcon />}
        >
          {loading ? 'Criando...' : 'Criar Pedido'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default OrderCreationDialog;