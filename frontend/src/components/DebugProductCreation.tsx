import React, { useState } from 'react';
import { <PERSON><PERSON>, Box, Paper, Typography } from '@mui/material';
import { productService } from '../services/ProductService';
import { generateSku, CreateProductDto } from '../types/Product';

export const DebugProductCreation: React.FC = () => {
  const [result, setResult] = useState<string>('');
  const [loading, setLoading] = useState(false);
  
  console.log('🐛 DebugProductCreation component rendered!');

  const testProductCreation = async () => {
    setLoading(true);
    setResult('Testing product creation...\n');

    try {
      // Create a minimal product payload
      const testProduct: CreateProductDto = {
        name: 'Debug Test Product',
        variations: [
          {
            variation: 'Cápsulas',
            sku: generateSku('TEST', 'CAP'),
            price: 0,
          }
        ]
      };

      setResult(prev => prev + '\n1. Original payload:\n' + JSON.stringify(testProduct, null, 2));

      // Try to create the product
      const response = await productService.createProduct(testProduct);
      
      setResult(prev => prev + '\n\n✅ SUCCESS! Product created:\n' + JSON.stringify(response, null, 2));
    } catch (error: any) {
      console.error('Debug error:', error);
      
      let errorDetails = '\n\n❌ ERROR DETAILS:\n';
      
      if (error.response) {
        errorDetails += `Status: ${error.response.status}\n`;
        errorDetails += `Status Text: ${error.response.statusText}\n`;
        
        if (error.response.data) {
          errorDetails += '\nResponse Data:\n';
          errorDetails += JSON.stringify(error.response.data, null, 2);
          
          // Check for validation messages
          if (error.response.data.message) {
            errorDetails += '\n\nValidation Messages:\n';
            if (Array.isArray(error.response.data.message)) {
              error.response.data.message.forEach((msg: string) => {
                errorDetails += `- ${msg}\n`;
              });
            } else {
              errorDetails += `- ${error.response.data.message}\n`;
            }
          }
        }
        
        // Log request data
        if (error.config && error.config.data) {
          errorDetails += '\n\nRequest Payload Sent:\n';
          errorDetails += error.config.data;
        }
      } else {
        errorDetails += 'Network error or no response from server\n';
        errorDetails += error.message;
      }
      
      setResult(prev => prev + errorDetails);
    }
    
    setLoading(false);
  };

  return (
    <Box sx={{ position: 'fixed', bottom: 100, right: 20, zIndex: 9999 }}>
      <Paper elevation={10} sx={{ p: 2, maxWidth: 600, maxHeight: 400, overflow: 'auto', backgroundColor: '#f0f0f0', border: '2px solid red' }}>
        <Typography variant="h6" gutterBottom style={{ color: 'red' }}>🐛 Debug Product Creation</Typography>
        <Button 
          variant="contained" 
          color="error" 
          onClick={testProductCreation}
          disabled={loading}
          fullWidth
          size="large"
        >
          {loading ? 'Testing...' : '🔍 Test Product Creation'}
        </Button>
        {result && (
          <Box sx={{ mt: 2 }}>
            <pre style={{ 
              fontSize: '12px', 
              whiteSpace: 'pre-wrap', 
              wordBreak: 'break-word',
              backgroundColor: '#f5f5f5',
              padding: '10px',
              borderRadius: '4px'
            }}>
              {result}
            </pre>
          </Box>
        )}
      </Paper>
    </Box>
  );
};