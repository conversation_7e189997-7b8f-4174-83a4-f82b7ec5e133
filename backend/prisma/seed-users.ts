import { PrismaClient } from '@prisma/client';
import * as bcrypt from 'bcrypt';

const prisma = new PrismaClient();

async function main() {
  // Hash das senhas
  const hashedPassword = await bcrypt.hash('senha123', 10);

  // Criar ADMIN
  await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Administra<PERSON>',
      password: hashedPassword,
      role: 'ADMIN',
      active: true,
    },
  });

  // Criar SUPERVISOR
  await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Supervisor Teste',
      password: hashedPassword,
      role: 'SUPERVISOR',
      active: true,
    },
  });

  // Criar VENDEDOR
  await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: '<PERSON><PERSON><PERSON><PERSON>',
      password: hashedPassword,
      role: 'VENDED<PERSON>',
      active: true,
    },
  });

  // Criar COBRADOR
  await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Cobrador Teste',
      password: hashedPassword,
      role: 'COBRADOR',
      active: true,
    },
  });

  // Criar usuário inativo
  await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Usuário Inativo',
      password: hashedPassword,
      role: 'VENDEDOR',
      active: false,
    },
  });

  console.log('✅ Usuários de teste criados:');
  console.log('<EMAIL> / senha123 (ADMIN)');
  console.log('<EMAIL> / senha123 (SUPERVISOR)');
  console.log('<EMAIL> / senha123 (VENDEDOR)');
  console.log('<EMAIL> / senha123 (COBRADOR)');
  console.log('<EMAIL> / senha123 (VENDEDOR - Inativo)');
}

main()
  .then(async () => {
    await prisma.$disconnect();
  })
  .catch(async (e) => {
    console.error(e);
    await prisma.$disconnect();
    process.exit(1);
  });