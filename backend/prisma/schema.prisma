// Multi-Tenant Strategy:
// This schema implements a shared database with shared schema approach.
// Each tenant is isolated by a tenantId field on core business entities.
// - User, Order, Product, Customer, Kit, and Configuration are tenant-specific
// - Unique constraints are scoped to tenantId where appropriate
// - All queries must include tenantId filters to ensure data isolation
// - Consider implementing Row Level Security (RLS) at the database level for additional security

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                 String               @id @default(uuid())
  name               String
  email              String
  password           String
  role               Role
  active             Boolean              @default(true)
  tenantId           String               // Multi-tenant: User belongs to a specific tenant
  createdAt          DateTime             @default(now())
  updatedAt          DateTime             @updatedAt
  commissionPayments CommissionPayment[]
  commissionSetting  CommissionSetting?
  ordersAsCollector  Order[]              @relation("CollectorOrders")
  ordersAsSeller     Order[]              @relation("SellerOrders")
  orderStatusChanges OrderStatusHistory[]
  paymentApprovals   PaymentApproval[]

  @@unique([email, tenantId]) // Multi-tenant: Email must be unique per tenant
  @@index([tenantId])
}

model Order {
  id                       String                    @id @default(uuid())
  orderNumber              String?
  customerName             String
  customerPhone            String
  status                   OrderStatus               @default(Analise)
  total                    Decimal                   @db.Decimal(10, 2)
  sellerId                 String
  tenantId                 String                    // Multi-tenant: Order belongs to a specific tenant
  createdAt                DateTime                  @default(now())
  updatedAt                DateTime                  @updatedAt
  commissionApprovalStatus CommissionApprovalStatus? @default(NONE)
  paymentReceivedAmount    Decimal?                  @db.Decimal(10, 2)
  paymentReceivedDate      DateTime?
  commissionApproved       Boolean                   @default(false)
  customerId               String?
  collectorId              String?
  nextPaymentDate          DateTime?
  lastContactDate          DateTime?
  zapId                    String?
  
  // Anti-fraud fields
  customerCPF              String?                   @db.VarChar(64) // Encrypted
  customerCPFHash          String?                   // For searching
  fullAddress              String?
  isDuplicate              Boolean                   @default(false)
  duplicateStatus          DuplicateStatus?
  duplicateMatchScore      Int?
  duplicateCheckVersion    String?
  originalOrderIds         String[]
  reviewedBy               String?
  reviewedByName           String?
  reviewedByRole           String?
  reviewedAt               DateTime?
  reviewDecision           ReviewDecision?
  reviewDuration           Int?                      // milliseconds
  
  // Relations
  commissionPayments       CommissionPayment[]
  collector                User?                     @relation("CollectorOrders", fields: [collectorId], references: [id])
  customer                 Customer?                 @relation(fields: [customerId], references: [id])
  seller                   User                      @relation("SellerOrders", fields: [sellerId], references: [id])
  items                    OrderItem[]
  statusHistory            OrderStatusHistory[]
  paymentApprovals         PaymentApproval[]
  tracking                 Tracking?
  addressComponents        OrderAddressComponents?
  auditLogs                OrderAuditLog[]

  @@unique([orderNumber, tenantId]) // Multi-tenant: Order number must be unique per tenant
  @@index([orderNumber])
  @@index([tenantId])
  @@index([tenantId, isDuplicate, duplicateStatus])
  @@index([tenantId, customerCPFHash])
  @@index([tenantId, createdAt])
  @@index([reviewedBy, reviewedAt])
}

model OrderItem {
  id                 String            @id @default(uuid())
  orderId            String
  productVariationId String?
  productId          String
  productName        String
  quantity           Int
  unitPrice          Decimal           @db.Decimal(10, 2)
  createdAt          DateTime          @default(now())
  updatedAt          DateTime          @updatedAt
  order              Order             @relation(fields: [orderId], references: [id], onDelete: Cascade)
  productVariation   ProductVariation? @relation(fields: [productVariationId], references: [id])
}

model OrderStatusHistory {
  id             String      @id @default(uuid())
  orderId        String
  previousStatus OrderStatus
  newStatus      OrderStatus
  changedAt      DateTime    @default(now())
  changedById    String
  changedBy      User        @relation(fields: [changedById], references: [id])
  order          Order       @relation(fields: [orderId], references: [id], onDelete: Cascade)
}

model Product {
  id          String             @id @default(uuid())
  name        String
  description String?
  imageUrl    String?
  price       Decimal            @db.Decimal(10, 2) @default(0)
  active      Boolean            @default(true)
  tenantId    String             // Multi-tenant: Product belongs to a specific tenant
  createdAt   DateTime           @default(now())
  updatedAt   DateTime           @updatedAt
  variations  ProductVariation[]

  @@index([tenantId])
}

model ProductVariation {
  id         String      @id @default(uuid())
  productId  String
  variation  String
  price      Decimal     @db.Decimal(10, 2)
  sku        String      @unique // SKU globally unique for now, can be scoped per tenant via application logic
  active     Boolean     @default(true)
  createdAt  DateTime    @default(now())
  updatedAt  DateTime    @updatedAt
  inventory  Inventory?
  kitItems   KitItem[]
  orderItems OrderItem[]
  product    Product     @relation(fields: [productId], references: [id], onDelete: Cascade)
}

model Inventory {
  id                 String           @id @default(uuid())
  productVariationId String           @unique
  quantity           Int              @default(0)
  minAlert           Int              @default(10)
  updatedAt          DateTime         @updatedAt
  productVariation   ProductVariation @relation(fields: [productVariationId], references: [id], onDelete: Cascade)
}

model Kit {
  id          String    @id @default(uuid())
  name        String
  description String?
  price       Decimal   @default(0) @db.Decimal(10, 2)
  active      Boolean   @default(true)
  tenantId    String    // Multi-tenant: Kit belongs to a specific tenant
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  items       KitItem[]

  @@index([tenantId])
}

model KitItem {
  id                 String           @id @default(uuid())
  kitId              String
  productVariationId String
  quantity           Int
  kit                Kit              @relation(fields: [kitId], references: [id], onDelete: Cascade)
  productVariation   ProductVariation @relation(fields: [productVariationId], references: [id])

  @@unique([kitId, productVariationId])
}

model Customer {
  id        String    @id @default(uuid())
  name      String
  cpf       String
  phone     String
  email     String?
  active    Boolean   @default(true)
  tenantId  String    // Multi-tenant: Customer belongs to a specific tenant
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  addresses Address[]
  orders    Order[]

  @@unique([cpf, tenantId]) // Multi-tenant: CPF must be unique per tenant
  @@index([tenantId])
}

model Address {
  id           String   @id @default(uuid())
  customerId   String
  cep          String
  street       String
  number       String
  neighborhood String
  city         String
  state        String   @db.VarChar(2)
  complement   String?
  main         Boolean  @default(false)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  customer     Customer @relation(fields: [customerId], references: [id], onDelete: Cascade)

  @@index([customerId])
}

model NotificationJob {
  id        String              @id @default(uuid())
  type      NotificationType
  payload   Json
  channel   NotificationChannel @default(WHATSAPP)
  status    NotificationStatus  @default(PENDING)
  retries   Int                 @default(0)
  error     String?
  sentAt    DateTime?
  createdAt DateTime            @default(now())
  updatedAt DateTime            @updatedAt
}

model Tracking {
  id          String    @id @default(uuid())
  code        String    @unique
  status      String
  lastUpdate  DateTime
  events      Json      @default("[]")
  orderId     String    @unique
  lastSync    DateTime?
  isDelivered Boolean   @default(false)
  hasAlert    Boolean   @default(false)
  alertReason String?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  order       Order     @relation(fields: [orderId], references: [id], onDelete: Cascade)

  @@index([status])
  @@index([hasAlert])
}

model Configuration {
  id        String   @id @default(uuid())
  key       String
  value     Json
  tenantId  String   // Multi-tenant: Configuration belongs to a specific tenant
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([key, tenantId]) // Multi-tenant: Configuration key must be unique per tenant
  @@index([tenantId])
}

model CommissionSetting {
  id         String   @id @default(uuid())
  userId     String   @unique
  percentage Decimal  @db.Decimal(5, 2)
  role       Role
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  user       User     @relation(fields: [userId], references: [id])
}

model PaymentApproval {
  id              String                   @id @default(uuid())
  orderId         String
  approvedById    String
  previousStatus  OrderStatus
  newStatus       OrderStatus
  paymentAmount   Decimal                  @db.Decimal(10, 2)
  paymentType     String
  approvalStatus  CommissionApprovalStatus
  rejectionReason String?
  createdAt       DateTime                 @default(now())
  updatedAt       DateTime                 @updatedAt
  approvedBy      User                     @relation(fields: [approvedById], references: [id])
  order           Order                    @relation(fields: [orderId], references: [id])
}

model CommissionPayment {
  id               String   @id @default(uuid())
  orderId          String
  userId           String
  userRole         Role
  baseAmount       Decimal  @db.Decimal(10, 2)
  percentage       Decimal  @db.Decimal(5, 2)
  commissionAmount Decimal  @db.Decimal(10, 2)
  paymentDate      DateTime
  createdAt        DateTime @default(now())
  order            Order    @relation(fields: [orderId], references: [id])
  user             User     @relation(fields: [userId], references: [id])

  @@unique([orderId, userId, paymentDate])
}

enum Role {
  ADMIN
  SUPERVISOR
  COBRADOR
  VENDEDOR
}

enum OrderStatus {
  PagamentoPendente
  Completo
  Parcial
  Cancelado
  Transito
  Analise
  Separacao
  Frustrado
  Recuperacao
  Negociacao
  RetirarCorreios
  EntregaFalha
  ConfirmarEntrega
  DevolvidoCorreios
}

enum NotificationType {
  ORDER_CREATED
  STATUS_CHANGED
  LOW_STOCK
  PAYMENT_REMINDER
  CUSTOM
}

enum NotificationChannel {
  WHATSAPP
  EMAIL
  SMS
}

enum NotificationStatus {
  PENDING
  PROCESSING
  SENT
  FAILED
}

enum CommissionApprovalStatus {
  NONE
  PENDING
  APPROVED
  REJECTED
}

enum DuplicateStatus {
  PENDING_REVIEW
  APPROVED
  DENIED
  AUTO_APPROVED
}

enum ReviewDecision {
  APPROVE_ORDER
  DENY_ORDER
  MERGE_ORDERS
  INVESTIGATE_FURTHER
}

enum AuditAction {
  ORDER_CREATED
  ORDER_UPDATED
  DUPLICATE_DETECTED
  DUPLICATE_REVIEWED
  DUPLICATE_APPROVED
  DUPLICATE_DENIED
  ADDRESS_PARSED
  ADDRESS_GEOCODED
  MANUAL_OVERRIDE
  SYSTEM_OVERRIDE
}

model OrderAddressComponents {
  id                    String   @id @default(uuid())
  orderId               String   @unique
  order                 Order    @relation(fields: [orderId], references: [id], onDelete: Cascade)
  
  // Parsed Components
  street                String
  streetNumber          String
  complement            String?
  neighborhood          String
  city                  String
  state                 String   @db.VarChar(2)
  zipCode               String   @db.VarChar(8)
  
  // Normalized for Matching
  streetNormalized      String
  streetSoundex         String
  streetMetaphone       String
  neighborhoodNorm      String
  neighborhoodSoundex   String
  cityNormalized        String
  
  // Geocoding (Optional)
  latitude              Float?
  longitude             Float?
  
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt
  
  @@index([streetSoundex, cityNormalized])
  @@index([zipCode])
  @@index([streetNormalized, streetNumber])
}

model OrderAuditLog {
  id                    String   @id @default(uuid())
  orderId               String
  order                 Order    @relation(fields: [orderId], references: [id], onDelete: Cascade)
  tenantId              String
  
  // Audit Data
  action                AuditAction
  performedBy           String
  performedByName       String
  performedByRole       String
  performedAt           DateTime @default(now())
  
  // Change Details
  previousData          Json?
  newData               Json?
  metadata              Json?    // IP, user agent, etc.
  
  // Security
  signature             String   // Cryptographic signature
  signatureAlgorithm    String   @default("SHA256")
  
  @@index([orderId, performedAt])
  @@index([tenantId, action, performedAt])
  @@index([performedBy, performedAt])
}
