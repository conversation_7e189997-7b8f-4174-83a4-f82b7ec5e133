import { ApiProperty } from '@nestjs/swagger';
import { Decimal } from '@prisma/client/runtime/library';

export class ReceberHojeItemDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  customerName: string;

  @ApiProperty()
  customerPhone: string;

  @ApiProperty()
  total: Decimal;

  @ApiProperty()
  status: string;

  @ApiProperty()
  sellerName: string;

  @ApiProperty({ required: false })
  collectorName?: string;

  @ApiProperty({ required: false })
  lastContactDate?: Date;

  @ApiProperty({ required: false })
  nextPaymentDate?: Date;
}

export class ReceberHojeResponseDto {
  @ApiProperty({ type: [ReceberHojeItemDto] })
  items: ReceberHojeItemDto[];

  @ApiProperty()
  total: number;

  @ApiProperty()
  totalValue: Decimal;
}