import {
  Injectable,
  NotFoundException,
  ForbiddenException,
  BadRequestException,
} from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateOrderDto } from './dto/create-order.dto';
import { UpdateStatusDto } from './dto/update-status.dto';
import { OrderFilterDto } from './dto/order-filter.dto';
import { Order, OrderStatus, Prisma, Role } from '@prisma/client';
import { Decimal } from '@prisma/client/runtime/library';
import { NotificationsService } from '../notifications/notifications.service';
import { AntifraudService } from '../antifraud/antifraud.service';

@Injectable()
export class OrdersService {
  constructor(
    private prisma: PrismaService,
    private notificationsService: NotificationsService,
    private antifraudService: AntifraudService,
  ) {}

  private async generateOrderNumber(): Promise<string> {
    // Get the last order number
    const lastOrder = await this.prisma.order.findFirst({
      where: {
        orderNumber: {
          not: null
        }
      },
      orderBy: {
        createdAt: 'desc',
      },
      select: {
        orderNumber: true,
      },
    });

    let nextNumber = 1;
    
    if (lastOrder && lastOrder.orderNumber) {
      // Extract the numeric part from the last order number
      const matches = lastOrder.orderNumber.match(/ID(\d+)/);
      if (matches && matches[1]) {
        nextNumber = parseInt(matches[1], 10) + 1;
      }
    }

    // Format with ID prefix and pad with zeros (5 digits)
    return `ID${nextNumber.toString().padStart(5, '0')}`;
  }

  async create(createOrderDto: CreateOrderDto, sellerId: string): Promise<Order> {
    // Get tenant ID from seller
    const seller = await this.prisma.user.findUnique({
      where: { id: sellerId },
      select: { tenantId: true },
    });
    
    if (!seller || !seller.tenantId) {
      throw new BadRequestException('Invalid seller or tenant');
    }
    
    const tenantId = seller.tenantId;

    // Gerar número do pedido
    const orderNumber = await this.generateOrderNumber();
    
    // Calcular total do pedido
    const total = createOrderDto.items.reduce(
      (sum, item) => sum + item.quantity * item.unitPrice,
      0,
    );

    // Criar pedido com itens e histórico inicial
    const order = await this.prisma.order.create({
      data: {
        orderNumber,
        customerId: createOrderDto.customerId,
        customerName: createOrderDto.customerName,
        customerPhone: createOrderDto.customerPhone,
        total: new Decimal(total),
        sellerId,
        tenantId,
        collectorId: createOrderDto.collectorId,
        zapId: createOrderDto.zapId,
        items: {
          create: createOrderDto.items.map((item) => ({
            productId: item.productId,
            productName: item.productName,
            quantity: item.quantity,
            unitPrice: new Decimal(item.unitPrice),
          })),
        },
        statusHistory: {
          create: {
            previousStatus: OrderStatus.Analise,
            newStatus: OrderStatus.Analise,
            changedById: sellerId,
          },
        },
      },
      include: {
        items: true,
        customer: true,
        seller: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        collector: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    // Check for duplicates if CPF and address are provided
    if (createOrderDto.customerCPF && createOrderDto.fullAddress) {
      // Run anti-fraud check asynchronously to not block order creation
      this.antifraudService.processOrderForDuplicates(
        tenantId,
        order.id,
        createOrderDto.customerCPF,
        createOrderDto.fullAddress,
      ).catch(err => {
        console.error('Error processing anti-fraud check:', err);
      });
    }

    // Envia notificação de pedido criado (async, não bloqueia)
    this.notificationsService.notifyOrderCreated(order).catch(err => {
      console.error('Erro ao enviar notificação de pedido criado:', err);
    });

    return order;
  }

  async findAll(
    filters: OrderFilterDto,
    userId: string,
    userRole: Role,
  ): Promise<Order[]> {
    const where: Prisma.OrderWhereInput = {};

    // Aplicar filtros
    if (filters.status) {
      where.status = filters.status;
    }

    if (filters.sellerId) {
      where.sellerId = filters.sellerId;
    }

    if (filters.collectorId) {
      where.collectorId = filters.collectorId;
    }

    if (filters.startDate || filters.endDate) {
      where.createdAt = {};
      if (filters.startDate) {
        where.createdAt.gte = new Date(filters.startDate);
      }
      if (filters.endDate) {
        where.createdAt.lte = new Date(filters.endDate);
      }
    }

    // Aplicar restrições por role
    if (userRole === Role.VENDEDOR) {
      where.sellerId = userId;
    } else if (userRole === Role.COBRADOR) {
      where.collectorId = userId;
    }
    // ADMIN e SUPERVISOR veem todos

    const orders = await this.prisma.order.findMany({
      where,
      include: {
        items: true,
        seller: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        collector: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    return orders;
  }

  async findOne(id: string, userId: string, userRole: Role): Promise<Order> {
    const order = await this.prisma.order.findUnique({
      where: { id },
      include: {
        items: true,
        seller: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        collector: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        statusHistory: {
          include: {
            changedBy: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
          orderBy: {
            changedAt: 'desc',
          },
        },
      },
    });

    if (!order) {
      throw new NotFoundException('Pedido não encontrado');
    }

    // Verificar permissões
    if (userRole === Role.VENDEDOR && order.sellerId !== userId) {
      throw new ForbiddenException('Você não tem permissão para ver este pedido');
    }

    if (userRole === Role.COBRADOR && order.collectorId !== userId) {
      throw new ForbiddenException('Você não tem permissão para ver este pedido');
    }

    return order;
  }

  async updateStatus(
    id: string,
    updateStatusDto: UpdateStatusDto,
    userId: string,
    userRole: Role,
  ): Promise<Order> {
    // Buscar pedido atual
    const order = await this.prisma.order.findUnique({
      where: { id },
    });

    if (!order) {
      throw new NotFoundException('Pedido não encontrado');
    }

    // Verificar permissões
    if (userRole === Role.VENDEDOR) {
      throw new ForbiddenException('Vendedores não podem alterar status de pedidos');
    }

    if (userRole === Role.COBRADOR) {
      // Cobradores só podem alterar pedidos atribuídos a eles
      if (order.collectorId !== userId) {
        throw new ForbiddenException('Você não tem permissão para alterar este pedido');
      }
      // Cobradores podem marcar como COMPLETO ou PAGAMENTO_PARCIAL
      if (updateStatusDto.status !== OrderStatus.Completo && 
          updateStatusDto.status !== OrderStatus.Parcial &&
          updateStatusDto.status !== OrderStatus.Frustrado &&
          updateStatusDto.status !== OrderStatus.EntregaFalha &&
          updateStatusDto.status !== OrderStatus.Recuperacao &&
          updateStatusDto.status !== OrderStatus.Negociacao &&
          updateStatusDto.status !== OrderStatus.Transito &&
          updateStatusDto.status !== OrderStatus.PagamentoPendente &&
          updateStatusDto.status !== OrderStatus.ConfirmarEntrega) {
        throw new ForbiddenException('Cobradores só podem alterar para status permitidos');
      }
    }

    // Validar transição de status
    if (!this.isValidStatusTransition(order.status, updateStatusDto.status)) {
      throw new BadRequestException(
        `Transição inválida de ${order.status} para ${updateStatusDto.status}`,
      );
    }

    // Atualizar status e criar histórico
    const updatedOrder = await this.prisma.order.update({
      where: { id },
      data: {
        status: updateStatusDto.status,
        statusHistory: {
          create: {
            previousStatus: order.status,
            newStatus: updateStatusDto.status,
            changedById: userId,
          },
        },
      },
      include: {
        items: true,
        seller: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        collector: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    // Envia notificação de mudança de status (async, não bloqueia)
    this.notificationsService.notifyStatusChanged(
      id,
      order.status,
      updateStatusDto.status,
    ).catch(err => {
      console.error('Erro ao enviar notificação de mudança de status:', err);
    });

    return updatedOrder;
  }

  async remove(id: string, userId: string, userRole: Role): Promise<Order> {
    const order = await this.prisma.order.findUnique({
      where: { id },
    });

    if (!order) {
      throw new NotFoundException('Pedido não encontrado');
    }

    // Apenas ADMIN pode cancelar qualquer pedido
    // VENDEDOR pode cancelar seu próprio pedido se estiver PENDENTE
    if (userRole === Role.VENDEDOR) {
      if (order.sellerId !== userId) {
        throw new ForbiddenException('Você não tem permissão para cancelar este pedido');
      }
      if (order.status !== OrderStatus.Analise) {
        throw new BadRequestException('Apenas pedidos em análise podem ser cancelados');
      }
    } else if (userRole === Role.COBRADOR) {
      throw new ForbiddenException('Cobradores não podem cancelar pedidos');
    } else if (userRole === Role.SUPERVISOR && order.status !== OrderStatus.Analise) {
      throw new BadRequestException('Supervisores só podem cancelar pedidos em análise');
    }

    // Atualizar para CANCELADO
    const cancelledOrder = await this.prisma.order.update({
      where: { id },
      data: {
        status: OrderStatus.Cancelado,
        statusHistory: {
          create: {
            previousStatus: order.status,
            newStatus: OrderStatus.Cancelado,
            changedById: userId,
          },
        },
      },
      include: {
        items: true,
        seller: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        collector: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    return cancelledOrder;
  }

  private isValidStatusTransition(from: OrderStatus, to: OrderStatus): boolean {
    const validTransitions: { [key in OrderStatus]?: OrderStatus[] } = {
      Analise: [OrderStatus.Separacao, OrderStatus.Cancelado],
      Separacao: [OrderStatus.Transito, OrderStatus.Cancelado],
      Transito: [OrderStatus.ConfirmarEntrega, OrderStatus.EntregaFalha, OrderStatus.RetirarCorreios, OrderStatus.DevolvidoCorreios],
      ConfirmarEntrega: [OrderStatus.Completo, OrderStatus.PagamentoPendente, OrderStatus.Negociacao, OrderStatus.Parcial],
      PagamentoPendente: [OrderStatus.Completo, OrderStatus.Parcial, OrderStatus.Negociacao, OrderStatus.Frustrado],
      Negociacao: [OrderStatus.Recuperacao, OrderStatus.Cancelado, OrderStatus.Completo, OrderStatus.Parcial, OrderStatus.Frustrado],
      Parcial: [OrderStatus.Completo, OrderStatus.Negociacao],
      Completo: [], // Estado final
      Recuperacao: [OrderStatus.Transito, OrderStatus.Negociacao, OrderStatus.Cancelado, OrderStatus.Completo, OrderStatus.Parcial],
      Frustrado: [], // Estado final
      EntregaFalha: [OrderStatus.Recuperacao, OrderStatus.Negociacao, OrderStatus.Frustrado],
      RetirarCorreios: [OrderStatus.Frustrado, OrderStatus.Cancelado],
      DevolvidoCorreios: [OrderStatus.Frustrado, OrderStatus.Cancelado],
      Cancelado: [], // Estado final
    };

    return validTransitions[from]?.includes(to) || false;
  }
}