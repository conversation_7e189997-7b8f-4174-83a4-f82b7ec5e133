import {
  Controller,
  Get,
  Post,
  Patch,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  ValidationPipe,
  UsePipes,
} from '@nestjs/common';
import { OrdersService } from './orders.service';
import { CreateOrderDto } from './dto/create-order.dto';
import { UpdateStatusDto } from './dto/update-status.dto';
import { OrderFilterDto } from './dto/order-filter.dto';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { RolesGuard } from '../auth/roles.guard';
import { Roles } from '../auth/roles.decorator';

@Controller('orders')
@UseGuards(JwtAuthGuard, RolesGuard)
export class OrdersController {
  constructor(private readonly ordersService: OrdersService) {}

  @Post()
  @Roles('VENDEDOR', 'ADMIN', 'SUPERVISOR')
  @UsePipes(new ValidationPipe({ whitelist: true }))
  async create(@Body() createOrderDto: CreateOrderDto, @Request() req) {
    return this.ordersService.create(createOrderDto, req.user.userId);
  }

  @Get()
  @Roles('VENDEDOR', 'COBRADOR', 'ADMIN', 'SUPERVISOR')
  async findAll(@Query(ValidationPipe) filters: OrderFilterDto, @Request() req) {
    return this.ordersService.findAll(filters, req.user.userId, req.user.role);
  }

  @Get(':id')
  @Roles('VENDEDOR', 'COBRADOR', 'ADMIN', 'SUPERVISOR')
  async findOne(@Param('id') id: string, @Request() req) {
    return this.ordersService.findOne(id, req.user.userId, req.user.role);
  }

  @Patch(':id/status')
  @Roles('COBRADOR', 'ADMIN', 'SUPERVISOR')
  @UsePipes(new ValidationPipe({ whitelist: true }))
  async updateStatus(
    @Param('id') id: string,
    @Body() updateStatusDto: UpdateStatusDto,
    @Request() req,
  ) {
    return this.ordersService.updateStatus(
      id,
      updateStatusDto,
      req.user.userId,
      req.user.role,
    );
  }

  @Delete(':id')
  @Roles('VENDEDOR', 'ADMIN', 'SUPERVISOR')
  async remove(@Param('id') id: string, @Request() req) {
    return this.ordersService.remove(id, req.user.userId, req.user.role);
  }
}