import { Test, TestingModule } from '@nestjs/testing';
import { CustomersService } from './customers.service';
import { PrismaService } from '../prisma/prisma.service';
import { ConflictException, BadRequestException } from '@nestjs/common';

describe('CustomersService', () => {
  let service: CustomersService;
  let prisma: PrismaService;

  const mockPrismaService = {
    customer: {
      create: jest.fn(),
      findUnique: jest.fn(),
      findFirst: jest.fn(),
      findMany: jest.fn(),
      update: jest.fn(),
      count: jest.fn(),
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CustomersService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
      ],
    }).compile();

    service = module.get<CustomersService>(CustomersService);
    prisma = module.get<PrismaService>(PrismaService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('create', () => {
    const createCustomerDto = {
      name: '<PERSON>',
      cpf: '123.456.789-01',
      phone: '(11) 98765-4321',
      email: '<EMAIL>',
    };

    it('should create a customer with valid CPF', async () => {
      const mockCustomer = {
        id: '1',
        name: 'João Silva',
        cpf: '12345678901',
        phone: '11987654321',
        email: '<EMAIL>',
        active: true,
        createdAt: new Date(),
        updatedAt: new Date(),
        addresses: [],
      };

      mockPrismaService.customer.findUnique.mockResolvedValue(null);
      mockPrismaService.customer.create.mockResolvedValue(mockCustomer);

      const result = await service.create(createCustomerDto);

      expect(result).toEqual(mockCustomer);
      expect(mockPrismaService.customer.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          name: 'João Silva',
          cpf: '12345678901', // CPF limpo
          phone: '11987654321', // Telefone limpo
          email: '<EMAIL>',
        }),
        include: { addresses: true },
      });
    });

    it('should throw BadRequestException for invalid CPF', async () => {
      const invalidCpfDto = {
        ...createCustomerDto,
        cpf: '111.111.111-11', // CPF inválido
      };

      await expect(service.create(invalidCpfDto)).rejects.toThrow(
        BadRequestException,
      );
    });

    it('should throw ConflictException if CPF already exists', async () => {
      mockPrismaService.customer.findUnique.mockResolvedValue({
        id: '2',
        cpf: '12345678901',
      });

      await expect(service.create(createCustomerDto)).rejects.toThrow(
        ConflictException,
      );
    });
  });

  describe('search', () => {
    it('should search customers by name', async () => {
      const mockCustomers = [
        {
          id: '1',
          name: 'João Silva',
          cpf: '12345678901',
          phone: '11987654321',
          email: '<EMAIL>',
          active: true,
          createdAt: new Date(),
          updatedAt: new Date(),
          addresses: [],
          _count: { orders: 5 },
        },
      ];

      mockPrismaService.customer.findMany.mockResolvedValue(mockCustomers);

      const result = await service.search({ name: 'João' });

      expect(result).toEqual(mockCustomers);
      expect(mockPrismaService.customer.findMany).toHaveBeenCalledWith({
        where: {
          AND: [
            {
              name: {
                contains: 'João',
                mode: 'insensitive',
              },
            },
          ],
        },
        include: expect.any(Object),
        orderBy: { name: 'asc' },
        take: 50,
      });
    });

    it('should search customers by CPF', async () => {
      mockPrismaService.customer.findMany.mockResolvedValue([]);

      await service.search({ cpf: '123.456.789-01' });

      expect(mockPrismaService.customer.findMany).toHaveBeenCalledWith({
        where: {
          AND: [
            {
              cpf: {
                contains: '12345678901', // CPF limpo
              },
            },
          ],
        },
        include: expect.any(Object),
        orderBy: { name: 'asc' },
        take: 50,
      });
    });
  });

  describe('formatCustomer', () => {
    it('should format customer with CPF and phone', () => {
      const customer: any = {
        id: '1',
        name: 'João Silva',
        cpf: '12345678901',
        phone: '11987654321',
        email: '<EMAIL>',
        addresses: [
          { id: '1', main: true, street: 'Rua A' },
          { id: '2', main: false, street: 'Rua B' },
        ],
      };

      const formatted = service.formatCustomer(customer);

      expect(formatted.cpf).toBe('123.456.789-01');
      expect(formatted.phone).toBe('(11) 98765-4321');
      expect(formatted.mainAddress).toEqual({ id: '1', main: true, street: 'Rua A' });
    });
  });
});