import { Worker, Job } from 'bullmq';
import { Injectable, OnModuleInit, OnModuleDestroy, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../../prisma/prisma.service';
import { WhatsAppProvider } from '../providers/whatsapp.provider';
import { TemplateParser } from '../templates/template-parser';
import { notificationTemplates, statusMessages } from '../templates/notification-templates';
import { NotificationJobData } from './notification.queue';
import { NotificationChannel, NotificationStatus } from '@prisma/client';

@Injectable()
export class NotificationWorker implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(NotificationWorker.name);
  private worker: Worker<NotificationJobData> | null = null;

  constructor(
    private configService: ConfigService,
    private prisma: PrismaService,
    private whatsappProvider: WhatsAppProvider,
  ) {}

  async onModuleInit() {
    try {
      const redisUrl = this.configService.get('REDIS_URL');
      let redisConfig;

      if (redisUrl) {
        try {
          // Try to parse as URL (e.g., redis://user:pass@host:port)
          const connectionOptions = new URL(redisUrl);
          redisConfig = {
            host: connectionOptions.hostname,
            port: Number(connectionOptions.port) || 6379,
            password: connectionOptions.password,
          };
        } catch (urlError) {
          // If not a valid URL, treat as hostname (e.g., redis.railway.internal)
          redisConfig = {
            host: redisUrl,
            port: this.configService.get('REDIS_PORT', 6379),
            password: this.configService.get('REDIS_PASSWORD'),
          };
        }
      } else {
        // Fallback to individual config
        redisConfig = {
          host: this.configService.get('REDIS_HOST', 'localhost'),
          port: this.configService.get('REDIS_PORT', 6379),
          password: this.configService.get('REDIS_PASSWORD'),
        };
      }

      this.worker = new Worker<NotificationJobData>(
        'notifications',
        async (job: Job<NotificationJobData>) => {
          return this.processNotification(job);
        },
        {
          connection: redisConfig,
          concurrency: 5, // Processa até 5 notificações simultaneamente
        },
      );

      this.worker.on('completed', (job) => {
        this.logger.log(`Notificação ${job.id} processada com sucesso`);
      });

      this.worker.on('failed', (job, err) => {
        this.logger.error(`Falha ao processar notificação ${job?.id}: ${err.message}`);
      });

      this.logger.log('Worker de notificações iniciado com Redis');
    } catch (error) {
      this.logger.warn('Redis não disponível. Worker de notificações desabilitado.');
      this.logger.debug(`Erro ao conectar ao Redis: ${error.message}`);
      // A aplicação continua funcionando sem o worker
    }
  }

  async onModuleDestroy() {
    await this.worker?.close();
  }

  /**
   * Processa uma notificação
   */
  private async processNotification(job: Job<NotificationJobData>) {
    const { data } = job;
    const notificationId = data.id || `job_${job.id}`;

    try {
      // Atualiza status para processando
      await this.updateNotificationStatus(notificationId, NotificationStatus.PROCESSING);

      // Prepara a mensagem com base no template
      const message = await this.prepareMessage(data);

      // Envia a notificação baseado no canal
      let result;
      switch (data.channel) {
        case NotificationChannel.WHATSAPP:
          result = await this.sendWhatsApp(data.recipient.phone!, message);
          break;
        case NotificationChannel.EMAIL:
          // TODO: Implementar provider de email
          throw new Error('Canal EMAIL ainda não implementado');
        case NotificationChannel.SMS:
          // TODO: Implementar provider de SMS
          throw new Error('Canal SMS ainda não implementado');
        default:
          throw new Error(`Canal desconhecido: ${data.channel}`);
      }

      if (result.success) {
        // Atualiza status para enviado
        await this.updateNotificationStatus(
          notificationId,
          NotificationStatus.SENT,
          null,
          new Date(),
        );

        return { success: true, messageId: result.messageId };
      } else {
        throw new Error(result.error || 'Falha no envio');
      }
    } catch (error: any) {
      // Log do erro
      this.logger.error(`Erro ao processar notificação ${notificationId}: ${error.message}`);

      // Atualiza status para falha
      await this.updateNotificationStatus(
        notificationId,
        NotificationStatus.FAILED,
        error.message,
      );

      // Re-throw para o BullMQ tentar novamente
      throw error;
    }
  }

  /**
   * Prepara a mensagem aplicando o template
   */
  private async prepareMessage(data: NotificationJobData): Promise<string> {
    const template = notificationTemplates[data.type];
    
    if (!template) {
      throw new Error(`Template não encontrado para tipo: ${data.type}`);
    }

    // Adiciona dados extras ao contexto
    const context: any = {
      ...data.payload,
      recipient: data.recipient,
    };

    // Se for mudança de status, adiciona mensagem específica
    if (data.type === 'STATUS_CHANGED' && context.newStatus) {
      context.statusMessage = statusMessages[context.newStatus] || '';
    }

    // Se for pedido, formata lista de itens
    if (context.order?.items) {
      context.order.itemsList = this.formatOrderItems(context.order.items);
    }

    // Valida placeholders
    const missingKeys = TemplateParser.validateTemplate(template.body, context);
    if (missingKeys.length > 0) {
      this.logger.warn(`Placeholders faltando: ${missingKeys.join(', ')}`);
    }

    // Aplica o template
    return TemplateParser.parse(template.body, context);
  }

  /**
   * Formata itens do pedido para exibição
   */
  private formatOrderItems(items: any[]): string {
    return items
      .map(item => `• ${item.quantity}x ${item.productName}`)
      .join('\n');
  }

  /**
   * Envia notificação via WhatsApp
   */
  private async sendWhatsApp(phone: string, message: string) {
    if (!phone) {
      throw new Error('Número de telefone não informado');
    }

    return this.whatsappProvider.sendMessage({
      to: phone,
      message,
    });
  }

  /**
   * Atualiza status da notificação no banco
   */
  private async updateNotificationStatus(
    id: string,
    status: NotificationStatus,
    error?: string | null,
    sentAt?: Date | null,
  ) {
    try {
      // Verifica se é um ID real ou um ID de job
      if (id.startsWith('job_')) {
        // É apenas um job, não tem registro no banco ainda
        return;
      }

      await this.prisma.notificationJob.update({
        where: { id },
        data: {
          status,
          error,
          sentAt,
          retries: {
            increment: status === NotificationStatus.FAILED ? 1 : 0,
          },
        },
      });
    } catch (error) {
      this.logger.warn(`Não foi possível atualizar status da notificação ${id}`);
    }
  }
}