import { Test, TestingModule } from '@nestjs/testing';
import { NotificationsService } from './notifications.service';
import { PrismaService } from '../prisma/prisma.service';
import { WhatsAppProvider } from './providers/whatsapp.provider';
import { EmailProvider } from './providers/email.provider';
import { TemplateParser } from './templates/template-parser';
import { ConfigurationService } from '../configuration/configuration.service';
import {
  NotificationType,
  NotificationChannel,
  NotificationStatus,
} from '@prisma/client';

describe('NotificationsService', () => {
  let service: NotificationsService;
  let prisma: PrismaService;
  let whatsappProvider: WhatsAppProvider;
  let emailProvider: EmailProvider;
  let templateParser: TemplateParser;
  let configService: ConfigurationService;

  const mockPrismaService = {
    notificationJob: {
      create: jest.fn(),
      findMany: jest.fn(),
      update: jest.fn(),
    },
  };

  const mockWhatsAppProvider = {
    send: jest.fn(),
  };

  const mockEmailProvider = {
    send: jest.fn(),
  };

  const mockTemplateParser = {
    parse: jest.fn(),
  };

  const mockConfigService = {
    get: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        NotificationsService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
        {
          provide: WhatsAppProvider,
          useValue: mockWhatsAppProvider,
        },
        {
          provide: EmailProvider,
          useValue: mockEmailProvider,
        },
        {
          provide: TemplateParser,
          useValue: mockTemplateParser,
        },
        {
          provide: ConfigurationService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<NotificationsService>(NotificationsService);
    prisma = module.get<PrismaService>(PrismaService);
    whatsappProvider = module.get<WhatsAppProvider>(WhatsAppProvider);
    emailProvider = module.get<EmailProvider>(EmailProvider);
    templateParser = module.get<TemplateParser>(TemplateParser);
    configService = module.get<ConfigurationService>(ConfigurationService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('sendNotification', () => {
    it('should create and send WhatsApp notification', async () => {
      const type = NotificationType.ORDER_CREATED;
      const data = {
        orderId: 'order-123',
        customerName: 'João Silva',
        customerPhone: '11999999999',
        total: 150.00,
      };

      const template = 'Olá {{customerName}}! Seu pedido #{{orderId}} foi criado.';
      const parsedMessage = 'Olá João Silva! Seu pedido #order-123 foi criado.';

      const mockJob = {
        id: 'job-123',
        type,
        payload: data,
        channel: NotificationChannel.WHATSAPP,
        status: NotificationStatus.PENDING,
      };

      mockConfigService.get.mockResolvedValue({
        orderCreated: { whatsapp: template },
      });
      mockTemplateParser.parse.mockReturnValue(parsedMessage);
      mockPrismaService.notificationJob.create.mockResolvedValue(mockJob);
      mockWhatsAppProvider.send.mockResolvedValue({ success: true });
      mockPrismaService.notificationJob.update.mockResolvedValue({
        ...mockJob,
        status: NotificationStatus.SENT,
        sentAt: new Date(),
      });

      const result = await service.sendNotification(type, data);

      expect(result.status).toBe(NotificationStatus.SENT);
      expect(mockWhatsAppProvider.send).toHaveBeenCalledWith(
        data.customerPhone,
        parsedMessage,
      );
      expect(mockPrismaService.notificationJob.update).toHaveBeenCalledWith({
        where: { id: 'job-123' },
        data: {
          status: NotificationStatus.SENT,
          sentAt: expect.any(Date),
        },
      });
    });

    it('should handle notification failure', async () => {
      const type = NotificationType.STATUS_CHANGED;
      const data = {
        orderId: 'order-123',
        customerPhone: '11999999999',
        newStatus: 'ENVIADO',
      };

      const mockJob = {
        id: 'job-123',
        type,
        payload: data,
        channel: NotificationChannel.WHATSAPP,
        status: NotificationStatus.PENDING,
        retries: 0,
      };

      mockConfigService.get.mockResolvedValue({
        statusChanged: { whatsapp: 'Status: {{newStatus}}' },
      });
      mockTemplateParser.parse.mockReturnValue('Status: ENVIADO');
      mockPrismaService.notificationJob.create.mockResolvedValue(mockJob);
      mockWhatsAppProvider.send.mockRejectedValue(new Error('API Error'));
      mockPrismaService.notificationJob.update.mockResolvedValue({
        ...mockJob,
        status: NotificationStatus.FAILED,
        retries: 1,
        error: 'API Error',
      });

      const result = await service.sendNotification(type, data);

      expect(result.status).toBe(NotificationStatus.FAILED);
      expect(result.error).toBe('API Error');
      expect(result.retries).toBe(1);
    });

    it('should send email notification when specified', async () => {
      const type = NotificationType.PAYMENT_REMINDER;
      const data = {
        customerEmail: '<EMAIL>',
        customerName: 'João Silva',
        orderId: 'order-123',
      };
      const channel = NotificationChannel.EMAIL;

      const template = {
        subject: 'Lembrete de pagamento - Pedido #{{orderId}}',
        body: 'Olá {{customerName}}, seu pedido aguarda pagamento.',
      };

      const mockJob = {
        id: 'job-123',
        type,
        payload: data,
        channel,
        status: NotificationStatus.PENDING,
      };

      mockConfigService.get.mockResolvedValue({
        paymentReminder: { email: template },
      });
      mockTemplateParser.parse
        .mockReturnValueOnce('Lembrete de pagamento - Pedido #order-123')
        .mockReturnValueOnce('Olá João Silva, seu pedido aguarda pagamento.');
      mockPrismaService.notificationJob.create.mockResolvedValue(mockJob);
      mockEmailProvider.send.mockResolvedValue({ success: true });
      mockPrismaService.notificationJob.update.mockResolvedValue({
        ...mockJob,
        status: NotificationStatus.SENT,
        sentAt: new Date(),
      });

      const result = await service.sendNotification(type, data, channel);

      expect(result.status).toBe(NotificationStatus.SENT);
      expect(mockEmailProvider.send).toHaveBeenCalledWith(
        data.customerEmail,
        'Lembrete de pagamento - Pedido #order-123',
        'Olá João Silva, seu pedido aguarda pagamento.',
      );
    });
  });

  describe('processPendingNotifications', () => {
    it('should process all pending notifications', async () => {
      const pendingJobs = [
        {
          id: 'job-1',
          type: NotificationType.ORDER_CREATED,
          payload: { customerPhone: '11999999999' },
          channel: NotificationChannel.WHATSAPP,
          status: NotificationStatus.PENDING,
          retries: 0,
        },
        {
          id: 'job-2',
          type: NotificationType.STATUS_CHANGED,
          payload: { customerPhone: '11888888888' },
          channel: NotificationChannel.WHATSAPP,
          status: NotificationStatus.PENDING,
          retries: 1,
        },
      ];

      mockPrismaService.notificationJob.findMany.mockResolvedValue(pendingJobs);
      mockConfigService.get.mockResolvedValue({
        orderCreated: { whatsapp: 'Order created' },
        statusChanged: { whatsapp: 'Status changed' },
      });
      mockTemplateParser.parse.mockReturnValue('Parsed message');
      mockWhatsAppProvider.send.mockResolvedValue({ success: true });
      mockPrismaService.notificationJob.update.mockResolvedValue({});

      const result = await service.processPendingNotifications();

      expect(result.processed).toBe(2);
      expect(result.success).toBe(2);
      expect(result.failed).toBe(0);
      expect(mockPrismaService.notificationJob.findMany).toHaveBeenCalledWith({
        where: {
          status: {
            in: [NotificationStatus.PENDING, NotificationStatus.FAILED],
          },
          retries: {
            lt: 3,
          },
        },
        orderBy: {
          createdAt: 'asc',
        },
        take: 100,
      });
    });
  });

  describe('getNotificationHistory', () => {
    it('should return notification history with filters', async () => {
      const filters = {
        type: NotificationType.ORDER_CREATED,
        channel: NotificationChannel.WHATSAPP,
        status: NotificationStatus.SENT,
        startDate: new Date('2024-01-01'),
        endDate: new Date('2024-01-31'),
      };

      const mockHistory = [
        {
          id: 'job-1',
          type: NotificationType.ORDER_CREATED,
          channel: NotificationChannel.WHATSAPP,
          status: NotificationStatus.SENT,
          createdAt: new Date('2024-01-15'),
        },
      ];

      mockPrismaService.notificationJob.findMany.mockResolvedValue(mockHistory);

      const result = await service.getNotificationHistory(filters);

      expect(result).toEqual(mockHistory);
      expect(mockPrismaService.notificationJob.findMany).toHaveBeenCalledWith({
        where: {
          type: filters.type,
          channel: filters.channel,
          status: filters.status,
          createdAt: {
            gte: filters.startDate,
            lte: filters.endDate,
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        take: 100,
      });
    });
  });

  describe('retryFailedNotifications', () => {
    it('should retry failed notifications', async () => {
      const failedJobs = [
        {
          id: 'job-1',
          type: NotificationType.ORDER_CREATED,
          payload: { customerPhone: '11999999999' },
          channel: NotificationChannel.WHATSAPP,
          status: NotificationStatus.FAILED,
          retries: 1,
        },
      ];

      mockPrismaService.notificationJob.findMany.mockResolvedValue(failedJobs);
      mockConfigService.get.mockResolvedValue({
        orderCreated: { whatsapp: 'Order created' },
      });
      mockTemplateParser.parse.mockReturnValue('Parsed message');
      mockWhatsAppProvider.send.mockResolvedValue({ success: true });
      mockPrismaService.notificationJob.update.mockResolvedValue({
        ...failedJobs[0],
        status: NotificationStatus.SENT,
        retries: 2,
        sentAt: new Date(),
      });

      const result = await service.retryFailedNotifications();

      expect(result.retried).toBe(1);
      expect(result.success).toBe(1);
      expect(mockPrismaService.notificationJob.update).toHaveBeenCalledWith({
        where: { id: 'job-1' },
        data: {
          status: NotificationStatus.SENT,
          sentAt: expect.any(Date),
          retries: 2,
        },
      });
    });
  });
});