import { Test, TestingModule } from '@nestjs/testing';
import { ConfigurationService } from './configuration.service';
import { PrismaService } from '../prisma/prisma.service';
import { ConfigService } from '@nestjs/config';
import { NotFoundException } from '@nestjs/common';

describe('ConfigurationService', () => {
  let service: ConfigurationService;
  let prisma: PrismaService;
  let configService: ConfigService;

  const mockPrismaService = {
    configuration: {
      upsert: jest.fn(),
      findUnique: jest.fn(),
      findMany: jest.fn(),
      delete: jest.fn(),
      count: jest.fn(),
    },
  };

  const mockConfigService = {
    get: jest.fn().mockReturnValue('test-encryption-key-32-characters'),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ConfigurationService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<ConfigurationService>(ConfigurationService);
    prisma = module.get<PrismaService>(PrismaService);
    configService = module.get<ConfigService>(ConfigService);

    // Limpar cache antes de cada teste
    service.clearCache();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('set', () => {
    it('should create a new configuration', async () => {
      const key = 'test_key';
      const value = { foo: 'bar' };

      mockPrismaService.configuration.upsert.mockResolvedValue({
        id: '123',
        key,
        value: JSON.stringify(value),
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      await service.set(key, value);

      expect(mockPrismaService.configuration.upsert).toHaveBeenCalledWith({
        where: { key },
        create: expect.objectContaining({
          key,
          value: expect.any(String),
        }),
        update: expect.objectContaining({
          value: expect.any(String),
        }),
      });
    });

    it('should encrypt sensitive keys', async () => {
      const key = 'stripe_secret_key';
      const value = 'sk_test_1234567890';

      mockPrismaService.configuration.upsert.mockResolvedValue({
        id: '123',
        key,
        value: 'encrypted_value',
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      await service.set(key, value);

      // Verifica se o valor salvo não é o original (está criptografado)
      const savedValue = mockPrismaService.configuration.upsert.mock.calls[0][0].create.value;
      expect(savedValue).not.toBe(JSON.stringify(value));
      expect(savedValue).toContain('encrypted');
      expect(savedValue).toContain('authTag');
      expect(savedValue).toContain('iv');
    });

    it('should not encrypt non-sensitive keys', async () => {
      const key = 'system_preferences';
      const value = { theme: 'dark' };

      mockPrismaService.configuration.upsert.mockResolvedValue({
        id: '123',
        key,
        value: JSON.stringify(value),
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      await service.set(key, value);

      const savedValue = mockPrismaService.configuration.upsert.mock.calls[0][0].create.value;
      expect(savedValue).toBe(JSON.stringify(value));
    });
  });

  describe('get', () => {
    it('should return cached value if available', async () => {
      const key = 'cached_key';
      const value = { cached: true };

      // Primeiro, define o valor para adicionar ao cache
      mockPrismaService.configuration.upsert.mockResolvedValue({});
      await service.set(key, value);

      // Limpa o mock para verificar que não será chamado novamente
      mockPrismaService.configuration.findUnique.mockClear();

      // Busca o valor (deve vir do cache)
      const result = await service.get(key);

      expect(result).toEqual(value);
      expect(mockPrismaService.configuration.findUnique).not.toHaveBeenCalled();
    });

    it('should fetch from database if not cached', async () => {
      const key = 'db_key';
      const value = { fromDb: true };

      mockPrismaService.configuration.findUnique.mockResolvedValue({
        id: '123',
        key,
        value: JSON.stringify(value),
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      const result = await service.get(key);

      expect(result).toEqual(value);
      expect(mockPrismaService.configuration.findUnique).toHaveBeenCalledWith({
        where: { key },
      });
    });

    it('should return null for non-existent key', async () => {
      mockPrismaService.configuration.findUnique.mockResolvedValue(null);

      const result = await service.get('non_existent');

      expect(result).toBeNull();
    });

    it('should decrypt encrypted values', async () => {
      const key = 'whatsapp_api_key';
      const originalValue = 'wa_secret_123';

      // Simula um valor criptografado no banco
      const encryptedData = {
        encrypted: 'some_encrypted_string',
        authTag: 'auth_tag',
        iv: 'initialization_vector',
      };

      mockPrismaService.configuration.findUnique.mockResolvedValue({
        id: '123',
        key,
        value: JSON.stringify(encryptedData),
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      // Para este teste, vamos mockar o método privado decrypt
      // Como é privado, vamos testar através do comportamento esperado
      try {
        await service.get(key);
      } catch (error) {
        // Esperamos um erro pois não temos a chave real de criptografia
        expect(error.message).toContain('Falha ao descriptografar');
      }
    });
  });

  describe('getAll', () => {
    it('should return all configurations', async () => {
      const configs = [
        {
          id: '1',
          key: 'config1',
          value: JSON.stringify({ value: 1 }),
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: '2',
          key: 'config2',
          value: JSON.stringify({ value: 2 }),
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      mockPrismaService.configuration.findMany.mockResolvedValue(configs);

      const result = await service.getAll();

      expect(result).toEqual({
        config1: { value: 1 },
        config2: { value: 2 },
      });
      expect(mockPrismaService.configuration.findMany).toHaveBeenCalled();
    });
  });

  describe('delete', () => {
    it('should delete an existing configuration', async () => {
      const key = 'to_delete';

      mockPrismaService.configuration.findUnique.mockResolvedValue({
        id: '123',
        key,
        value: '{}',
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      mockPrismaService.configuration.delete.mockResolvedValue({});

      await service.delete(key);

      expect(mockPrismaService.configuration.delete).toHaveBeenCalledWith({
        where: { key },
      });
    });

    it('should throw NotFoundException for non-existent key', async () => {
      mockPrismaService.configuration.findUnique.mockResolvedValue(null);

      await expect(service.delete('non_existent')).rejects.toThrow(
        NotFoundException,
      );
    });

    it('should remove from cache after deletion', async () => {
      const key = 'cached_to_delete';
      const value = { toDelete: true };

      // Adiciona ao cache
      mockPrismaService.configuration.upsert.mockResolvedValue({});
      await service.set(key, value);

      // Configura mocks para delete
      mockPrismaService.configuration.findUnique.mockResolvedValue({
        id: '123',
        key,
        value: JSON.stringify(value),
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      mockPrismaService.configuration.delete.mockResolvedValue({});

      // Deleta
      await service.delete(key);

      // Verifica se foi removido do cache
      expect(service.getCached(key)).toBeUndefined();
    });
  });

  describe('exists', () => {
    it('should return true for cached key', async () => {
      const key = 'cached_exists';
      const value = { exists: true };

      // Adiciona ao cache
      mockPrismaService.configuration.upsert.mockResolvedValue({});
      await service.set(key, value);

      mockPrismaService.configuration.count.mockClear();

      const result = await service.exists(key);

      expect(result).toBe(true);
      expect(mockPrismaService.configuration.count).not.toHaveBeenCalled();
    });

    it('should check database for non-cached key', async () => {
      mockPrismaService.configuration.count.mockResolvedValue(1);

      const result = await service.exists('db_exists');

      expect(result).toBe(true);
      expect(mockPrismaService.configuration.count).toHaveBeenCalledWith({
        where: { key: 'db_exists' },
      });
    });

    it('should return false for non-existent key', async () => {
      mockPrismaService.configuration.count.mockResolvedValue(0);

      const result = await service.exists('non_existent');

      expect(result).toBe(false);
    });
  });

  describe('getMultiple', () => {
    it('should return multiple configurations', async () => {
      const keys = ['key1', 'key2', 'key3'];
      
      mockPrismaService.configuration.findUnique
        .mockResolvedValueOnce({
          id: '1',
          key: 'key1',
          value: JSON.stringify({ value: 1 }),
          createdAt: new Date(),
          updatedAt: new Date(),
        })
        .mockResolvedValueOnce({
          id: '2',
          key: 'key2',
          value: JSON.stringify({ value: 2 }),
          createdAt: new Date(),
          updatedAt: new Date(),
        })
        .mockResolvedValueOnce(null);

      const result = await service.getMultiple(keys);

      expect(result).toEqual({
        key1: { value: 1 },
        key2: { value: 2 },
        key3: null,
      });
    });
  });

  describe('setMultiple', () => {
    it('should set multiple configurations', async () => {
      const configs = {
        key1: { value: 1 },
        key2: { value: 2 },
      };

      mockPrismaService.configuration.upsert.mockResolvedValue({});

      await service.setMultiple(configs);

      expect(mockPrismaService.configuration.upsert).toHaveBeenCalledTimes(2);
    });
  });

  describe('loadToMemory', () => {
    it('should load all configurations to cache', async () => {
      const configs = [
        {
          id: '1',
          key: 'memory1',
          value: JSON.stringify({ loaded: true }),
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: '2',
          key: 'stripe_secret_key',
          value: JSON.stringify({ encrypted: 'data' }),
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      mockPrismaService.configuration.findMany.mockResolvedValue(configs);

      await service.loadToMemory();

      expect(mockPrismaService.configuration.findMany).toHaveBeenCalled();
    });
  });

  describe('clearCache', () => {
    it('should clear all cached values', async () => {
      const key = 'to_clear';
      const value = { clear: true };

      // Adiciona ao cache
      mockPrismaService.configuration.upsert.mockResolvedValue({});
      await service.set(key, value);

      // Verifica que está no cache
      expect(service.getCached(key)).toEqual(value);

      // Limpa o cache
      service.clearCache();

      // Verifica que não está mais no cache
      expect(service.getCached(key)).toBeUndefined();
    });
  });
});