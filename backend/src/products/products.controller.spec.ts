import { Test, TestingModule } from '@nestjs/testing';
import { ProductsController } from './products.controller';
import { ProductsService } from './products.service';
import { CreateProductDto } from './dto/create-product.dto';

describe('ProductsController', () => {
  let controller: ProductsController;
  let service: ProductsService;

  const mockProductsService = {
    create: jest.fn(),
    findAll: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
    remove: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ProductsController],
      providers: [
        {
          provide: ProductsService,
          useValue: mockProductsService,
        },
      ],
    }).compile();

    controller = module.get<ProductsController>(ProductsController);
    service = module.get<ProductsService>(ProductsService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('create', () => {
    it('should create a product with valid data', async () => {
      const createProductDto: CreateProductDto = {
        name: 'Test Product',
        variations: [
          {
            variation: 'CAPSULAS',
            sku: 'S1',
            price: 100,
          },
        ],
      };

      const expectedResult = {
        id: '123',
        name: 'Test Product',
        active: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockProductsService.create.mockResolvedValue(expectedResult);

      const result = await controller.create(createProductDto);

      expect(service.create).toHaveBeenCalledWith(createProductDto);
      expect(result).toEqual(expectedResult);
    });

    it('should create a product with multiple variations', async () => {
      const createProductDto: CreateProductDto = {
        name: 'Multi Variation Product',
        description: 'A product with multiple variations',
        variations: [
          {
            variation: 'CAPSULAS',
            sku: 'MV-CAP-001',
            price: 150,
          },
          {
            variation: 'GOTAS',
            sku: 'MV-GOT-001',  
            price: 120,
          },
        ],
      };

      const expectedResult = {
        id: '456',
        name: 'Multi Variation Product',
        description: 'A product with multiple variations',
        active: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockProductsService.create.mockResolvedValue(expectedResult);

      const result = await controller.create(createProductDto);

      expect(service.create).toHaveBeenCalledWith(createProductDto);
      expect(result).toEqual(expectedResult);
    });
  });
});