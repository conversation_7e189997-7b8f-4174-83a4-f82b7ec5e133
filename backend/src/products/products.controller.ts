import {
  Controller,
  Get,
  Post,
  Patch,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  ParseBoolPipe,
} from '@nestjs/common';
import { ProductsService } from './products.service';
import { CreateProductDto } from './dto/create-product.dto';
import { UpdateProductDto } from './dto/update-product.dto';
import { CreateVariationDto } from './dto/create-variation.dto';
import { UpdateInventoryDto } from './dto/update-inventory.dto';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { RolesGuard } from '../auth/roles.guard';
import { Roles } from '../auth/roles.decorator';
import { TenantId } from '../common/decorators/tenant.decorator';

@Controller('products')
@UseGuards(JwtAuthGuard, RolesGuard)
export class ProductsController {
  constructor(private readonly productsService: ProductsService) {}

  // Rotas de Produtos
  @Post()
  @Roles('ADMIN', 'SUPERVISOR')
  async create(
    @Body() createProductDto: CreateProductDto,
    @TenantId() tenantId: string,
  ) {
    console.log('=== Product Creation Debug ===');
    console.log('Received payload:', JSON.stringify(createProductDto, null, 2));
    console.log('Payload type:', typeof createProductDto);
    console.log('Is Array?', Array.isArray(createProductDto));
    console.log('TenantId:', tenantId);
    console.log('=============================');
    
    return this.productsService.create(createProductDto, tenantId);
  }

  @Get()
  @Roles('ADMIN', 'SUPERVISOR', 'VENDEDOR', 'COBRADOR')
  async findAll(
    @TenantId() tenantId: string,
    @Query('active', new ParseBoolPipe({ optional: true })) active?: boolean,
  ) {
    console.log('=== Get Products Debug ===');
    console.log('Active filter:', active);
    console.log('TenantId:', tenantId);
    const result = await this.productsService.findAll(tenantId, active);
    console.log('Result type:', typeof result);
    console.log('Is Array?', Array.isArray(result));
    console.log('Result count:', result?.length);
    console.log('==========================');
    
    return result;
  }

  @Get('low-stock')
  @Roles('ADMIN', 'SUPERVISOR')
  async getLowStock(@TenantId() tenantId: string) {
    return this.productsService.getLowStockVariations(tenantId);
  }

  @Get(':id')
  @Roles('ADMIN', 'SUPERVISOR', 'VENDEDOR', 'COBRADOR')
  async findOne(
    @Param('id') id: string,
    @TenantId() tenantId: string,
  ) {
    return this.productsService.findOne(id, tenantId);
  }

  @Patch(':id')
  @Roles('ADMIN', 'SUPERVISOR')
  async update(
    @Param('id') id: string,
    @Body() updateProductDto: UpdateProductDto,
    @TenantId() tenantId: string,
  ) {
    return this.productsService.update(id, updateProductDto, tenantId);
  }

  @Delete(':id')
  @Roles('ADMIN', 'SUPERVISOR')
  async remove(
    @Param('id') id: string,
    @TenantId() tenantId: string,
  ) {
    return this.productsService.remove(id, tenantId);
  }

  // Rotas de Variações
  @Post(':id/variations')
  @Roles('ADMIN', 'SUPERVISOR')
  async createVariation(
    @Param('id') productId: string,
    @Body() createVariationDto: CreateVariationDto,
    @TenantId() tenantId: string,
  ) {
    return this.productsService.createVariation(productId, createVariationDto, tenantId);
  }

  @Patch('variations/:id')
  @Roles('ADMIN', 'SUPERVISOR')
  async updateVariation(
    @Param('id') variationId: string,
    @Body() data: { price?: number; active?: boolean },
  ) {
    return this.productsService.updateVariation(variationId, data);
  }

  // Rotas de Estoque
  @Patch('variations/:id/inventory')
  @Roles('ADMIN', 'SUPERVISOR')
  async updateInventory(
    @Param('id') variationId: string,
    @Body() updateInventoryDto: UpdateInventoryDto,
  ) {
    return this.productsService.updateInventory(variationId, updateInventoryDto);
  }

  @Get('variations/:id/stock')
  @Roles('ADMIN', 'SUPERVISOR', 'VENDEDOR', 'COBRADOR')
  async checkStock(@Param('id') variationId: string) {
    return this.productsService.checkStock(variationId);
  }

  @Post('variations/:id/stock/adjust')
  @Roles('ADMIN', 'SUPERVISOR')
  async adjustStock(
    @Param('id') variationId: string,
    @Body() data: { quantity: number; operation: 'add' | 'subtract' },
  ) {
    return this.productsService.adjustStock(variationId, data.quantity, data.operation);
  }
}