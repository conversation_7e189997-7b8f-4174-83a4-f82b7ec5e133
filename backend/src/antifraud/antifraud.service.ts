import * as crypto from "crypto";
import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { Prisma, DuplicateStatus, ReviewDecision, AuditAction } from '@prisma/client';
import { DuplicateDetectionService } from './services/duplicate-detection.service';
import { BrazilianAddressParser } from './services/brazilian-address.parser';
import { EncryptionUtil } from '../common/utils/encryption.util';
import { ConfigService } from '@nestjs/config';

export interface ReviewDuplicateDto {
  orderId: string;
  decision: ReviewDecision;
  reviewerId: string;
  reviewerName: string;
  reviewerRole: string;
  notes?: string;
}

export interface DuplicateReviewQueueItem {
  id: string;
  orderNumber: string;
  customerName: string;
  customerPhone: string;
  total: number;
  createdAt: Date;
  duplicateMatchScore: number;
  matchedOrders: Array<{
    orderId: string;
    orderNumber: string;
    createdAt: Date;
  }>;
}

@Injectable()
export class AntifraudService {
  private readonly logger = new Logger(AntifraudService.name);
  private readonly encryptionKey: string;
  private readonly signingKey: string;

  constructor(
    private prisma: PrismaService,
    private configService: ConfigService,
    private duplicateDetection: DuplicateDetectionService,
    private addressParser: BrazilianAddressParser,
    private encryptionUtil: EncryptionUtil,
  ) {
    this.encryptionKey = this.configService.get<string>('ENCRYPTION_KEY', '');
    this.signingKey = this.configService.get<string>('SIGNING_KEY', '');
  }

  /**
   * Process an order for duplicate detection
   */
  async processOrderForDuplicates(
    tenantId: string,
    orderId: string,
    customerCPF: string,
    fullAddress: string,
  ): Promise<void> {
    try {
      // Encrypt CPF
      const encryptedCPF = this.encryptionUtil.encrypt(customerCPF);
      const cpfHash = this.encryptionUtil.hash(customerCPF);

      // Parse address
      const parsedAddress = this.addressParser.parseAddress(fullAddress);
      
      // Check for duplicates
      const duplicateResult = await this.duplicateDetection.checkForDuplicates(
        tenantId,
        customerCPF,
        fullAddress,
        orderId,
      );

      // Update order with duplicate check results
      await this.prisma.$transaction(async (tx) => {
        // Update order
        await tx.order.update({
          where: { id: orderId },
          data: {
            customerCPF: encryptedCPF,
            customerCPFHash: cpfHash,
            fullAddress,
            isDuplicate: duplicateResult.isDuplicate,
            duplicateStatus: duplicateResult.isDuplicate
              ? DuplicateStatus.PENDING_REVIEW
              : null,
            duplicateMatchScore: duplicateResult.isDuplicate
              ? Math.round(duplicateResult.matchScore * 100)
              : null,
            duplicateCheckVersion: duplicateResult.algorithmVersion,
            originalOrderIds: duplicateResult.matchedOrders.map(m => m.orderId),
          },
        });

        // Save address components
        await this.duplicateDetection.saveAddressComponents(orderId, parsedAddress);

        // Create audit log
        await this.createAuditLog(tx, {
          orderId,
          tenantId,
          action: duplicateResult.isDuplicate
            ? AuditAction.DUPLICATE_DETECTED
            : AuditAction.ORDER_CREATED,
          performedBy: 'SYSTEM',
          performedByName: 'Anti-Fraud System',
          performedByRole: 'SYSTEM',
          metadata: {
            duplicateCheckResult: duplicateResult,
          },
        });
      });

      if (duplicateResult.isDuplicate) {
        this.logger.warn('Duplicate order detected', {
          orderId,
          matchScore: duplicateResult.matchScore,
          matchedOrders: duplicateResult.matchedOrders.length,
        });
      }
    } catch (error) {
      this.logger.error('Error processing order for duplicates', error);
      throw error;
    }
  }

  /**
   * Get duplicate orders pending review
   */
  async getDuplicateReviewQueue(
    tenantId: string,
    page: number = 1,
    limit: number = 20,
  ): Promise<{
    items: DuplicateReviewQueueItem[];
    total: number;
    pages: number;
  }> {
    const skip = (page - 1) * limit;

    const [items, total] = await Promise.all([
      this.prisma.order.findMany({
        where: {
          tenantId,
          isDuplicate: true,
          duplicateStatus: DuplicateStatus.PENDING_REVIEW,
        },
        select: {
          id: true,
          orderNumber: true,
          customerName: true,
          customerPhone: true,
          total: true,
          createdAt: true,
          duplicateMatchScore: true,
          originalOrderIds: true,
        },
        orderBy: {
          createdAt: 'desc',
        },
        skip,
        take: limit,
      }),
      this.prisma.order.count({
        where: {
          tenantId,
          isDuplicate: true,
          duplicateStatus: DuplicateStatus.PENDING_REVIEW,
        },
      }),
    ]);

    // Fetch matched order details
    const itemsWithMatches = await Promise.all(
      items.map(async (item) => {
        const matchedOrders = await this.prisma.order.findMany({
          where: {
            id: { in: item.originalOrderIds },
          },
          select: {
            id: true,
            orderNumber: true,
            createdAt: true,
          },
        });

        return {
          ...item,
          orderNumber: item.orderNumber || 'N/A',
          total: item.total.toNumber(),
          duplicateMatchScore: item.duplicateMatchScore || 0,
          matchedOrders: matchedOrders.map(order => ({
            orderId: order.id,
            orderNumber: order.orderNumber || 'N/A',
            createdAt: order.createdAt,
          })),
        };
      }),
    );

    return {
      items: itemsWithMatches,
      total,
      pages: Math.ceil(total / limit),
    };
  }

  /**
   * Review a duplicate order
   */
  async reviewDuplicate(
    tenantId: string,
    reviewData: ReviewDuplicateDto,
  ): Promise<void> {
    const startTime = Date.now();

    await this.prisma.$transaction(async (tx) => {
      // Get the order
      const order = await tx.order.findFirst({
        where: {
          id: reviewData.orderId,
          tenantId,
          isDuplicate: true,
          duplicateStatus: DuplicateStatus.PENDING_REVIEW,
        },
      });

      if (!order) {
        throw new Error('Order not found or already reviewed');
      }

      // Update order with review decision
      const newStatus =
        reviewData.decision === ReviewDecision.APPROVE_ORDER
          ? DuplicateStatus.APPROVED
          : DuplicateStatus.DENIED;

      await tx.order.update({
        where: { id: reviewData.orderId },
        data: {
          duplicateStatus: newStatus,
          reviewedBy: reviewData.reviewerId,
          reviewedByName: reviewData.reviewerName,
          reviewedByRole: reviewData.reviewerRole,
          reviewedAt: new Date(),
          reviewDecision: reviewData.decision,
          reviewDuration: Date.now() - startTime,
        },
      });

      // Create audit log
      await this.createAuditLog(tx, {
        orderId: reviewData.orderId,
        tenantId,
        action:
          reviewData.decision === ReviewDecision.APPROVE_ORDER
            ? AuditAction.DUPLICATE_APPROVED
            : AuditAction.DUPLICATE_DENIED,
        performedBy: reviewData.reviewerId,
        performedByName: reviewData.reviewerName,
        performedByRole: reviewData.reviewerRole,
        metadata: {
          decision: reviewData.decision,
          notes: reviewData.notes,
          reviewDuration: Date.now() - startTime,
        },
      });
    });

    this.logger.log('Duplicate order reviewed', {
      orderId: reviewData.orderId,
      decision: reviewData.decision,
      reviewer: reviewData.reviewerId,
    });
  }

  /**
   * Get order audit trail
   */
  async getOrderAuditTrail(
    tenantId: string,
    orderId: string,
  ): Promise<any[]> {
    const logs = await this.prisma.orderAuditLog.findMany({
      where: {
        orderId,
        tenantId,
      },
      orderBy: {
        performedAt: 'desc',
      },
    });

    // Verify signatures
    return logs.map(log => {
      const isValid = this.verifyAuditLogSignature(log);
      return {
        ...log,
        signatureValid: isValid,
      };
    });
  }

  /**
   * Create an audit log entry with cryptographic signature
   */
  private async createAuditLog(
    tx: Prisma.TransactionClient,
    data: {
      orderId: string;
      tenantId: string;
      action: AuditAction;
      performedBy: string;
      performedByName: string;
      performedByRole: string;
      previousData?: any;
      newData?: any;
      metadata?: any;
    },
  ): Promise<void> {
    const auditData = {
      ...data,
      performedAt: new Date(),
    };

    // Generate signature
    const signature = this.generateSignature(
      JSON.stringify(auditData),
      this.signingKey,
    );

    await tx.orderAuditLog.create({
      data: {
        ...auditData,
        signature,
        signatureAlgorithm: 'SHA256',
      },
    });
  }

  /**
   * Verify audit log signature
   */
  private verifyAuditLogSignature(log: any): boolean {
    try {
      const { signature, signatureAlgorithm, ...data } = log;
      
      const expectedSignature = this.generateSignature(
        JSON.stringify(data),
        this.signingKey,
      );
      
      return signature === expectedSignature;
    } catch (error) {
      this.logger.warn('Error verifying audit log signature', error);
      return false;
    }
  }

  private generateSignature(data: string, key: string): string {
    return crypto.createHmac('sha256', key).update(data).digest('hex');
  }
}
