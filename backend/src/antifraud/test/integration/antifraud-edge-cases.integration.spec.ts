import { Test } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import * as request from 'supertest';
import { PrismaService } from '../../../prisma/prisma.service';
import { AntifraudModule } from '../../antifraud.module';
import * as crypto from 'crypto';
import { DuplicateStatus, ReviewDecision } from '@prisma/client';
import { AsyncLocalStorage } from 'async_hooks';

describe('Antifraud Edge Cases Integration Tests', () => {
  let app: INestApplication;
  let prisma: PrismaService;
  // Remove encryption service, use crypto directly
  const tenantId = 'test-tenant-edge-cases';
  const authToken = 'Bearer test-token';

  beforeAll(async () => {
    const moduleRef = await Test.createTestingModule({
      imports: [AntifraudModule],
    }).compile();

    app = moduleRef.createNestApplication();
    app.useGlobalPipes(new ValidationPipe());
    
    // Apply tenant middleware
    app.use((req: any, res: any, next: any) => {
      req.headers['x-tenant-id'] = tenantId;
      req.user = { id: 'test-user', tenantId, role: 'ADMIN' };
      next();
    });

    await app.init();

    prisma = moduleRef.get<PrismaService>(PrismaService);
    // Encryption handled by crypto module

    await cleanupTestData();
  });

  afterEach(async () => {
    await cleanupTestData();
  });

  afterAll(async () => {
    await app.close();
  });

  async function cleanupTestData() {
    await prisma.orderAuditLog.deleteMany({ where: { tenantId } });
    await prisma.orderAddressComponents.deleteMany({ where: { tenantId } });
    await prisma.order.deleteMany({ where: { tenantId } });
  }

  describe('Address Parsing Edge Cases', () => {
    it('should handle addresses with missing components', async () => {
      const orderData = {
        orderNumber: 'EDGE-001',
        customerName: 'Test User',
        customerCPF: '123.456.789-00',
        customerEmail: '<EMAIL>',
        fullAddress: 'São Paulo - SP', // Only city and state
        totalAmount: 100,
        paymentMethod: 'COD',
        items: [{ productId: 'prod-1', quantity: 1, price: 100 }],
      };

      const response = await request(app.getHttpServer())
        .post('/orders')
        .set('Authorization', authToken)
        .set('x-tenant-id', tenantId)
        .send(orderData);

      expect(response.status).toBe(201);

      // Check address components
      const addressComponents = await prisma.orderAddressComponents.findFirst({
        where: { orderId: response.body.id, tenantId },
      });
      expect(addressComponents).toBeDefined();
      expect(addressComponents.city).toBe('sao paulo');
      expect(addressComponents.state).toBe('sp');
      expect(addressComponents.street).toBeNull();
    });

    it('should handle addresses with special characters and accents', async () => {
      const orderData = {
        orderNumber: 'EDGE-002',
        customerName: 'José São Paulo',
        customerCPF: '234.567.890-11',
        customerEmail: '<EMAIL>',
        fullAddress: 'Rua São José dos Côcos, 123, Pç. da Sé, São Paulo - SP',
        totalAmount: 150,
        paymentMethod: 'COD',
        items: [{ productId: 'prod-1', quantity: 1, price: 150 }],
      };

      const response = await request(app.getHttpServer())
        .post('/orders')
        .set('Authorization', authToken)
        .set('x-tenant-id', tenantId)
        .send(orderData);

      expect(response.status).toBe(201);

      const addressComponents = await prisma.orderAddressComponents.findFirst({
        where: { orderId: response.body.id, tenantId },
      });
      expect(addressComponents.street).toBe('sao jose dos cocos');
      expect(addressComponents.neighborhood).toBe('pc da se');
    });

    it('should handle very long addresses', async () => {
      const longAddress = 'Rua Professor Doutor José Maria da Silva Santos Júnior, 1234, ' +
        'Bloco A, Apartamento 567, Condomínio Residencial Parque das Flores Amarelas, ' +
        'Jardim São Francisco de Assis, São Paulo - SP, 12345-678';

      const orderData = {
        orderNumber: 'EDGE-003',
        customerName: 'Test User',
        customerCPF: '345.678.901-22',
        customerEmail: '<EMAIL>',
        fullAddress: longAddress,
        totalAmount: 200,
        paymentMethod: 'COD',
        items: [{ productId: 'prod-1', quantity: 2, price: 100 }],
      };

      const response = await request(app.getHttpServer())
        .post('/orders')
        .set('Authorization', authToken)
        .set('x-tenant-id', tenantId)
        .send(orderData);

      expect(response.status).toBe(201);
    });
  });

  describe('CPF Edge Cases', () => {
    it('should handle CPF with different formatting', async () => {
      // Create order with formatted CPF
      const firstOrder = await createTestOrder({
        customerCPF: '123.456.789-00',
        fullAddress: 'Rua A, 100',
      });

      // Create order with unformatted CPF (same CPF)
      const orderData = {
        orderNumber: 'EDGE-004',
        customerName: 'Same Person',
        customerCPF: '12345678900', // Same CPF without formatting
        customerEmail: '<EMAIL>',
        fullAddress: 'Rua A, 100',
        totalAmount: 100,
        paymentMethod: 'COD',
        items: [{ productId: 'prod-1', quantity: 1, price: 100 }],
      };

      const response = await request(app.getHttpServer())
        .post('/orders')
        .set('Authorization', authToken)
        .set('x-tenant-id', tenantId)
        .send(orderData);

      expect(response.status).toBe(201);
      expect(response.body.isDuplicate).toBe(true);
    });

    it('should handle CPF with leading zeros', async () => {
      const orderData = {
        orderNumber: 'EDGE-005',
        customerName: 'Test User',
        customerCPF: '000.000.001-91', // Valid CPF with leading zeros
        customerEmail: '<EMAIL>',
        fullAddress: 'Test Address',
        totalAmount: 100,
        paymentMethod: 'COD',
        items: [{ productId: 'prod-1', quantity: 1, price: 100 }],
      };

      const response = await request(app.getHttpServer())
        .post('/orders')
        .set('Authorization', authToken)
        .set('x-tenant-id', tenantId)
        .send(orderData);

      expect(response.status).toBe(201);
    });
  });

  describe('Concurrent Operations', () => {
    it('should handle concurrent duplicate checks for the same CPF', async () => {
      const promises = [];
      const cpf = '456.789.012-33';
      const address = 'Rua Teste, 100, São Paulo - SP';

      // Create 5 orders concurrently with same CPF
      for (let i = 0; i < 5; i++) {
        const orderData = {
          orderNumber: `CONCURRENT-${i}`,
          customerName: 'Concurrent User',
          customerCPF: cpf,
          customerEmail: `user${i}@example.com`,
          fullAddress: address,
          totalAmount: 100,
          paymentMethod: 'COD',
          items: [{ productId: 'prod-1', quantity: 1, price: 100 }],
        };

        promises.push(
          request(app.getHttpServer())
            .post('/orders')
            .set('Authorization', authToken)
            .set('x-tenant-id', tenantId)
            .send(orderData)
        );
      }

      const responses = await Promise.all(promises);

      // All should succeed
      responses.forEach(response => {
        expect(response.status).toBe(201);
      });

      // At least some should be marked as duplicates
      const duplicates = responses.filter(r => r.body.isDuplicate);
      expect(duplicates.length).toBeGreaterThan(0);
    });

    it('should handle concurrent review operations', async () => {
      // Create pending orders
      const order1 = await createTestOrder({
        orderNumber: 'REVIEW-001',
        customerCPF: '567.890.123-44',
        isDuplicate: true,
        duplicateStatus: DuplicateStatus.PENDING_REVIEW,
        duplicateMatchScore: 85,
      });

      const order2 = await createTestOrder({
        orderNumber: 'REVIEW-002',
        customerCPF: '678.901.234-55',
        isDuplicate: true,
        duplicateStatus: DuplicateStatus.PENDING_REVIEW,
        duplicateMatchScore: 90,
      });

      // Try to review both concurrently
      const [response1, response2] = await Promise.all([
        request(app.getHttpServer())
          .post(`/antifraud/duplicates/${order1.id}/review`)
          .set('Authorization', authToken)
          .set('x-tenant-id', tenantId)
          .send({ decision: ReviewDecision.APPROVE_ORDER, reason: 'Test 1' }),
        request(app.getHttpServer())
          .post(`/antifraud/duplicates/${order2.id}/review`)
          .set('Authorization', authToken)
          .set('x-tenant-id', tenantId)
          .send({ decision: ReviewDecision.DENY_ORDER, reason: 'Test 2' }),
      ]);

      expect(response1.status).toBe(200);
      expect(response2.status).toBe(200);
    });
  });

  describe('Performance and Limits', () => {
    it('should handle pagination correctly with many orders', async () => {
      // Create 25 pending orders
      const promises = [];
      for (let i = 0; i < 25; i++) {
        promises.push(
          createTestOrder({
            orderNumber: `BULK-${i.toString().padStart(3, '0')}`,
            customerCPF: `${i.toString().padStart(11, '0')}`,
            isDuplicate: true,
            duplicateStatus: DuplicateStatus.PENDING_REVIEW,
            duplicateMatchScore: 70 + i,
          })
        );
      }
      await Promise.all(promises);

      // Test first page
      const page1 = await request(app.getHttpServer())
        .get('/antifraud/duplicates/review-queue')
        .set('Authorization', authToken)
        .set('x-tenant-id', tenantId)
        .query({ page: 1, limit: 10 });

      expect(page1.status).toBe(200);
      expect(page1.body.items).toHaveLength(10);
      expect(page1.body.total).toBe(25);
      expect(page1.body.totalPages).toBe(3);

      // Test last page
      const page3 = await request(app.getHttpServer())
        .get('/antifraud/duplicates/review-queue')
        .set('Authorization', authToken)
        .set('x-tenant-id', tenantId)
        .query({ page: 3, limit: 10 });

      expect(page3.status).toBe(200);
      expect(page3.body.items).toHaveLength(5);
    });

    it('should respect duplicate check timeout', async () => {
      // This test would require mocking the duplicate detection service
      // to simulate a slow response
    });
  });

  describe('Multi-tenant Isolation', () => {
    it('should not detect duplicates across tenants', async () => {
      const otherTenantId = 'other-tenant';
      
      // Create order in first tenant
      const firstOrder = await createTestOrder({
        customerCPF: '789.012.345-66',
        fullAddress: 'Rua Isolada, 100',
      });

      // Try to create same order in different tenant
      const orderData = {
        orderNumber: 'TENANT-002',
        customerName: 'Other Tenant User',
        customerCPF: '789.012.345-66',
        customerEmail: '<EMAIL>',
        fullAddress: 'Rua Isolada, 100',
        totalAmount: 100,
        paymentMethod: 'COD',
        items: [{ productId: 'prod-1', quantity: 1, price: 100 }],
      };

      const response = await request(app.getHttpServer())
        .post('/orders')
        .set('Authorization', authToken)
        .set('x-tenant-id', otherTenantId)
        .send(orderData);

      expect(response.status).toBe(201);
      expect(response.body.isDuplicate).toBe(false); // Should not detect duplicate from other tenant

      // Cleanup other tenant data
      await prisma.order.deleteMany({ where: { tenantId: otherTenantId } });
    });

    it('should not allow reviewing orders from different tenant', async () => {
      // Create order in current tenant
      const order = await createTestOrder({
        orderNumber: 'TENANT-003',
        customerCPF: '890.123.456-77',
        isDuplicate: true,
        duplicateStatus: DuplicateStatus.PENDING_REVIEW,
        duplicateMatchScore: 85,
      });

      // Try to review with different tenant ID
      const response = await request(app.getHttpServer())
        .post(`/antifraud/duplicates/${order.id}/review`)
        .set('Authorization', authToken)
        .set('x-tenant-id', 'different-tenant')
        .send({ decision: ReviewDecision.APPROVE_ORDER, reason: 'Test' });

      expect(response.status).toBe(404); // Order not found in different tenant
    });
  });

  describe('Audit Trail Security', () => {
    it('should create cryptographically signed audit logs', async () => {
      const order = await createTestOrder({
        orderNumber: 'AUDIT-001',
        customerCPF: '901.234.567-88',
        isDuplicate: true,
        duplicateStatus: DuplicateStatus.PENDING_REVIEW,
        duplicateMatchScore: 92,
      });

      // Review the order
      await request(app.getHttpServer())
        .post(`/antifraud/duplicates/${order.id}/review`)
        .set('Authorization', authToken)
        .set('x-tenant-id', tenantId)
        .send({ decision: ReviewDecision.APPROVE_ORDER, reason: 'Security test' });

      // Get audit log
      const auditLog = await prisma.orderAuditLog.findFirst({
        where: { orderId: order.id, action: 'DUPLICATE_APPROVED', tenantId },
      });

      expect(auditLog).toBeDefined();
      expect(auditLog.signature).toBeDefined();
      expect(auditLog.signature.length).toBeGreaterThan(0);
    });
  });

  // Helper function
  async function createTestOrder(data: Partial<any>) {
    const encryptedCPF = data.customerCPF ? 
      crypto.createHash('sha256').update(data.customerCPF).digest('hex') : null;
    const cpfHash = data.customerCPF ? 
      crypto.createHash('sha256').update(data.customerCPF).digest('hex') : null;

    return prisma.order.create({
      data: {
        tenantId,
        orderNumber: data.orderNumber || 'TEST-001',
        customerName: data.customerName || 'Test Customer',
        customerCPF: encryptedCPF,
        customerCPFHash: cpfHash,
        customerEmail: data.customerEmail || '<EMAIL>',
        fullAddress: data.fullAddress || 'Test Address',
        totalAmount: data.totalAmount || 100,
        paymentMethod: data.paymentMethod || 'COD',
        status: data.status || 'PENDING',
        isDuplicate: data.isDuplicate || false,
        duplicateStatus: data.duplicateStatus || null,
        duplicateMatchScore: data.duplicateMatchScore || null,
        reviewDecision: data.reviewDecision || null,
      },
    });
  }
});