import { DuplicateMatch } from "./duplicate-detection-types";
import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { ConfigService } from '@nestjs/config';
import { Order, OrderStatus, DuplicateStatus } from '@prisma/client';
import { BrazilianAddressParser, ParsedAddress } from './brazilian-address.parser';
import { PhoneticEncoderService } from './phonetic-encoder.service';
import { FuzzyMatchingService, AddressComponents } from './fuzzy-matching.service';
import { EncryptionUtil } from '../../common/utils/encryption.util';

export interface DuplicateCheckResult {
  isDuplicate: boolean;
  matchScore: number;
  matchedOrders: Array<{
    orderId: string;
    orderNumber: string;
    matchScore: number;
    matchedComponents: string[];
    createdAt: Date;
  }>;
  checkDuration: number; // milliseconds
  algorithmVersion: string;
}

@Injectable()
export class DuplicateDetectionService {
  private readonly logger = new Logger(DuplicateDetectionService.name);
  private readonly algorithmVersion = '1.0.0';
  private readonly checkTimeoutMs: number;
  private readonly encryptionKey: string;

  constructor(
    private prisma: PrismaService,
    private configService: ConfigService,
    private addressParser: BrazilianAddressParser,
    private phoneticEncoder: PhoneticEncoderService,
    private fuzzyMatcher: FuzzyMatchingService,
    private encryptionUtil: EncryptionUtil,
  ) {
    this.checkTimeoutMs = this.configService.get<number>(
      'DUPLICATE_CHECK_TIMEOUT_MS',
      2000,
    );
    this.encryptionKey = this.configService.get<string>('ENCRYPTION_KEY', '');
  }

  /**
   * Check if an order is a duplicate based on CPF and address
   */
  async checkForDuplicates(
    tenantId: string,
    customerCPF: string,
    fullAddress: string,
    excludeOrderId?: string,
  ): Promise<DuplicateCheckResult> {
    const startTime = Date.now();

    try {
      // Parse the address
      const parsedAddress = this.addressParser.parseAddress(fullAddress);
      if (!this.addressParser.isValidAddress(parsedAddress)) {
        this.logger.warn('Invalid address format', { fullAddress });
        return this.createEmptyResult(startTime);
      }

      // Encrypt CPF and generate hash for searching
      const cpfHash = this.encryptionUtil.hash(customerCPF);

      // Find potential matches by CPF hash
      const potentialMatches = await this.findPotentialMatches(
        tenantId,
        cpfHash,
        excludeOrderId,
      );

      if (potentialMatches.length === 0) {
        return this.createEmptyResult(startTime);
      }

      // Check each potential match
      const matchedOrders = await this.evaluateMatches(
        potentialMatches,
        customerCPF,
        parsedAddress,
      );

      // Filter matches that meet the criteria (2+ address components)
      const duplicates = matchedOrders.filter(match => match.matchedComponents.length >= 2);

      return {
        isDuplicate: duplicates.length > 0,
        matchScore: duplicates.length > 0 ? Math.max(...duplicates.map(d => d.matchScore)) : 0,
        matchedOrders: duplicates,
        checkDuration: Date.now() - startTime,
        algorithmVersion: this.algorithmVersion,
      };
    } catch (error) {
      this.logger.error('Error checking for duplicates', error);
      return this.createEmptyResult(startTime);
    }
  }

  /**
   * Find potential duplicate orders by CPF hash
   */
  private async findPotentialMatches(
    tenantId: string,
    cpfHash: string,
    excludeOrderId?: string,
  ): Promise<Order[]> {
    const query = {
      where: {
        tenantId,
        customerCPFHash: cpfHash,
        status: {
          in: [
            OrderStatus.PagamentoPendente,
            OrderStatus.Analise,
            OrderStatus.Separacao,
            OrderStatus.Transito,
          ],
        },
        ...(excludeOrderId && { id: { not: excludeOrderId } }),
      },
      orderBy: {
        createdAt: 'desc' as const,
      },
      take: 100, // Limit to prevent performance issues
    };

    // Use timeout to ensure fast response
    return Promise.race([
      this.prisma.order.findMany(query),
      new Promise<Order[]>((_, reject) =>
        setTimeout(() => reject(new Error('Query timeout')), this.checkTimeoutMs),
      ),
    ]).catch(error => {
      this.logger.warn('Query timeout or error', error);
      return [];
    });
  }

  /**
   * Evaluate each potential match
   */
  private async evaluateMatches(
    potentialMatches: Order[],
    customerCPF: string,
    parsedAddress: ParsedAddress,
  ): Promise<Array<{
    orderId: string;
    orderNumber: string;
    matchScore: number;
    matchedComponents: string[];
    createdAt: Date;
  }>> {
    const matches: Array<{
      orderId: string;
      orderNumber: string;
      matchScore: number;
      matchedComponents: string[];
      createdAt: Date;
    }> = [];

    for (const order of potentialMatches) {
      try {
        // Decrypt and verify CPF
        if (order.customerCPF) {
          const decryptedCPF = this.encryptionUtil.decrypt(order.customerCPF);
          
          if (decryptedCPF !== customerCPF) {
            continue; // CPF doesn't match, skip
          }
        }

        // Compare addresses
        let addressMatch;
        
        // Get address components if they exist
        const addressComponents = await this.prisma.orderAddressComponents.findUnique({
          where: { orderId: order.id },
        });
        
        if (addressComponents) {
          // Use pre-parsed components
          addressMatch = this.fuzzyMatcher.calculateAddressMatch(
            this.convertToAddressComponents(addressComponents),
            this.convertParsedToComponents(parsedAddress),
          );
        } else if (order.fullAddress) {
          // Parse and compare
          const orderParsedAddress = this.addressParser.parseAddress(order.fullAddress);
          addressMatch = this.fuzzyMatcher.calculateAddressMatch(
            this.convertParsedToComponents(orderParsedAddress),
            this.convertParsedToComponents(parsedAddress),
          );
        } else {
          continue; // No address to compare
        }

        // Check if it meets duplicate criteria
        if (this.fuzzyMatcher.isDuplicateCandidate(true, addressMatch)) {
          matches.push({
            orderId: order.id,
            orderNumber: order.orderNumber || 'N/A',
            matchScore: addressMatch.score,
            matchedComponents: addressMatch.matchedComponents,
            createdAt: order.createdAt,
          });
          // Additional match data if needed
          // customerName: order.customerName || "Unknown", 
          // totalAmount: Number(order.total || 0)
        }
      } catch (error) {
        this.logger.warn('Error evaluating match', { orderId: order.id, error });
      }
    }

    // Sort by match score descending
    return matches.sort((a, b) => b.matchScore - a.matchScore);
  }

  /**
   * Convert database address components to matching format
   */
  private convertToAddressComponents(dbComponents: any): AddressComponents {
    return {
      street: dbComponents.street,
      streetNumber: dbComponents.streetNumber,
      neighborhood: dbComponents.neighborhood,
      city: dbComponents.city,
      state: dbComponents.state,
      zipCode: dbComponents.zipCode,
      streetSoundex: dbComponents.streetSoundex,
      streetMetaphone: dbComponents.streetMetaphone,
      neighborhoodSoundex: dbComponents.neighborhoodSoundex,
      streetNormalized: dbComponents.streetNormalized,
      neighborhoodNorm: dbComponents.neighborhoodNorm,
      cityNormalized: dbComponents.cityNormalized,
    };
  }

  /**
   * Convert parsed address to components format
   */
  private convertParsedToComponents(parsed: ParsedAddress): AddressComponents {
    return {
      street: parsed.street,
      streetNumber: parsed.streetNumber,
      neighborhood: parsed.neighborhood,
      city: parsed.city,
      state: parsed.state,
      zipCode: parsed.zipCode,
      streetNormalized: parsed.normalized.street,
      neighborhoodNorm: parsed.normalized.neighborhood,
      cityNormalized: parsed.normalized.city,
      streetSoundex: this.phoneticEncoder.soundexPT(parsed.street),
      streetMetaphone: this.phoneticEncoder.metaphonePT(parsed.street),
      neighborhoodSoundex: this.phoneticEncoder.soundexPT(parsed.neighborhood),
    };
  }

  /**
   * Create empty result
   */
  private createEmptyResult(startTime: number): DuplicateCheckResult {
    return {
      isDuplicate: false,
      matchScore: 0,
      matchedOrders: [],
      checkDuration: Date.now() - startTime,
      algorithmVersion: this.algorithmVersion,
    };
  }

  /**
   * Save address components for an order
   */
  async saveAddressComponents(
    orderId: string,
    parsedAddress: ParsedAddress,
  ): Promise<void> {
    try {
      const components = this.convertParsedToComponents(parsedAddress);
      
      await this.prisma.orderAddressComponents.create({
        data: {
          orderId,
          street: components.street,
          streetNumber: components.streetNumber,
          complement: parsedAddress.complement,
          neighborhood: components.neighborhood,
          city: components.city,
          state: components.state,
          zipCode: components.zipCode,
          streetNormalized: components.streetNormalized || '',
          streetSoundex: components.streetSoundex || '',
          streetMetaphone: components.streetMetaphone || '',
          neighborhoodNorm: components.neighborhoodNorm || '',
          neighborhoodSoundex: components.neighborhoodSoundex || '',
          cityNormalized: components.cityNormalized || '',
        },
      });
    } catch (error) {
      this.logger.error('Error saving address components', error);
    }
  }
}