import {
  Controller,
  Get,
  Post,
  Patch,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  ValidationPipe,
  UsePipes,
} from '@nestjs/common';
import { UsersService } from './users.service';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { RolesGuard } from '../auth/roles.guard';
import { Roles } from '../auth/roles.decorator';
import { Role } from '@prisma/client';
import { TenantId } from '../common/decorators/tenant.decorator';

@Controller('users')
@UseGuards(JwtAuthGuard, RolesGuard)
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Post()
  @Roles('ADMIN')
  @UsePipes(new ValidationPipe({ whitelist: true }))
  async create(
    @Body() createUserDto: CreateUserDto,
    @TenantId() tenantId: string,
  ) {
    return this.usersService.create(createUserDto, tenantId);
  }

  @Get()
  @Roles('ADMIN', 'SUPERVISOR')
  async findAll(
    @TenantId() tenantId: string,
    @Query('role') role?: Role,
    @Query('active') active?: string,
    @Query('email') email?: string,
  ) {
    const filters = {
      role,
      active: active === 'true' ? true : active === 'false' ? false : undefined,
      email,
    };

    return this.usersService.findAll(tenantId, filters);
  }

  @Get(':id')
  @Roles('ADMIN', 'SUPERVISOR')
  async findOne(
    @Param('id') id: string,
    @TenantId() tenantId: string,
  ) {
    return this.usersService.findOne(id, tenantId);
  }

  @Patch(':id')
  @Roles('ADMIN', 'SUPERVISOR')
  @UsePipes(new ValidationPipe({ whitelist: true }))
  async update(
    @Param('id') id: string,
    @Body() updateUserDto: UpdateUserDto,
    @Request() req,
    @TenantId() tenantId: string,
  ) {
    return this.usersService.update(id, updateUserDto, req.user.role, tenantId);
  }

  @Delete(':id')
  @Roles('ADMIN')
  async remove(
    @Param('id') id: string,
    @TenantId() tenantId: string,
  ) {
    return this.usersService.softDelete(id, tenantId);
  }
}