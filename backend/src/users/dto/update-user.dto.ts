import { IsString, IsEmail, IsEnum, IsBoolean, IsOptional } from 'class-validator';
import { Role } from '@prisma/client';

export class UpdateUserDto {
  @IsString()
  @IsOptional()
  name?: string;

  @IsEmail({}, { message: 'Email deve ser válido' })
  @IsOptional()
  email?: string;

  @IsEnum(Role, { message: 'Role deve ser ADMIN, SUPERVISOR, COBRADOR ou VENDEDOR' })
  @IsOptional()
  role?: Role;

  @IsBoolean()
  @IsOptional()
  active?: boolean;

  // Não permitir alteração de senha nesta sprint
}