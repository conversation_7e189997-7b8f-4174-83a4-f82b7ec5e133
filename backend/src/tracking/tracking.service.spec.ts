import { Test, TestingModule } from '@nestjs/testing';
import { TrackingService } from './tracking.service';
import { PrismaService } from '../prisma/prisma.service';
import { NotificationsService } from '../notifications/notifications.service';
import { CorreiosProvider } from './providers/correios.provider';
import { OrderStatus } from '@prisma/client';
import { NotFoundException } from '@nestjs/common';

describe('TrackingService', () => {
  let service: TrackingService;
  let prisma: PrismaService;
  let notificationsService: NotificationsService;
  let correiosProvider: CorreiosProvider;

  const mockPrismaService = {
    tracking: {
      create: jest.fn(),
      findUnique: jest.fn(),
      findFirst: jest.fn(),
      update: jest.fn(),
      findMany: jest.fn(),
    },
    order: {
      findUnique: jest.fn(),
      update: jest.fn(),
    },
  };

  const mockNotificationsService = {
    sendNotification: jest.fn(),
  };

  const mockCorreiosProvider = {
    trackPackage: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        TrackingService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
        {
          provide: NotificationsService,
          useValue: mockNotificationsService,
        },
        {
          provide: CorreiosProvider,
          useValue: mockCorreiosProvider,
        },
      ],
    }).compile();

    service = module.get<TrackingService>(TrackingService);
    prisma = module.get<PrismaService>(PrismaService);
    notificationsService = module.get<NotificationsService>(NotificationsService);
    correiosProvider = module.get<CorreiosProvider>(CorreiosProvider);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createTracking', () => {
    it('should create tracking for an order', async () => {
      const orderId = 'order-123';
      const trackingCode = 'BR123456789BR';
      
      const mockOrder = {
        id: orderId,
        status: OrderStatus.ENVIADO,
        customerId: 'customer-123',
      };

      const mockTracking = {
        id: 'tracking-123',
        orderId,
        code: trackingCode,
        status: 'Postado',
        lastUpdate: new Date(),
        events: [],
        isDelivered: false,
        hasAlert: false,
      };

      mockPrismaService.order.findUnique.mockResolvedValue(mockOrder);
      mockPrismaService.tracking.create.mockResolvedValue(mockTracking);
      mockCorreiosProvider.trackPackage.mockResolvedValue({
        code: trackingCode,
        status: 'Postado',
        lastUpdate: new Date(),
        events: [],
        isDelivered: false,
      });

      const result = await service.createTracking(orderId, trackingCode);

      expect(result).toEqual(mockTracking);
      expect(mockPrismaService.order.findUnique).toHaveBeenCalledWith({
        where: { id: orderId },
        include: { customer: true },
      });
      expect(mockPrismaService.tracking.create).toHaveBeenCalled();
    });

    it('should throw NotFoundException if order not found', async () => {
      mockPrismaService.order.findUnique.mockResolvedValue(null);

      await expect(
        service.createTracking('invalid-order', 'BR123456789BR'),
      ).rejects.toThrow(NotFoundException);
    });
  });

  describe('syncTracking', () => {
    it('should sync tracking with Correios and update status', async () => {
      const trackingCode = 'BR123456789BR';
      
      const mockTracking = {
        id: 'tracking-123',
        code: trackingCode,
        orderId: 'order-123',
        status: 'Em trânsito',
        isDelivered: false,
        order: {
          id: 'order-123',
          status: OrderStatus.ENVIADO,
          customerId: 'customer-123',
        },
      };

      const correiosResponse = {
        code: trackingCode,
        status: 'Entregue',
        lastUpdate: new Date(),
        events: [
          {
            timestamp: new Date(),
            status: 'Entregue',
            location: 'São Paulo - SP',
            description: 'Objeto entregue ao destinatário',
          },
        ],
        isDelivered: true,
      };

      mockPrismaService.tracking.findFirst.mockResolvedValue(mockTracking);
      mockCorreiosProvider.trackPackage.mockResolvedValue(correiosResponse);
      mockPrismaService.tracking.update.mockResolvedValue({
        ...mockTracking,
        ...correiosResponse,
      });
      mockPrismaService.order.update.mockResolvedValue({
        ...mockTracking.order,
        status: OrderStatus.ENTREGUE,
      });

      const result = await service.syncTracking(trackingCode);

      expect(result.status).toBe('Entregue');
      expect(result.isDelivered).toBe(true);
      expect(mockPrismaService.order.update).toHaveBeenCalledWith({
        where: { id: 'order-123' },
        data: { status: OrderStatus.ENTREGUE },
      });
    });

    it('should create alert for problematic status', async () => {
      const trackingCode = 'BR123456789BR';
      
      const mockTracking = {
        id: 'tracking-123',
        code: trackingCode,
        orderId: 'order-123',
        status: 'Em trânsito',
        hasAlert: false,
        order: {
          id: 'order-123',
          status: OrderStatus.ENVIADO,
          customer: {
            id: 'customer-123',
            name: 'João Silva',
          },
        },
      };

      const correiosResponse = {
        code: trackingCode,
        status: 'Destinatário ausente',
        lastUpdate: new Date(),
        events: [],
        isDelivered: false,
      };

      mockPrismaService.tracking.findFirst.mockResolvedValue(mockTracking);
      mockCorreiosProvider.trackPackage.mockResolvedValue(correiosResponse);
      mockPrismaService.tracking.update.mockResolvedValue({
        ...mockTracking,
        status: correiosResponse.status,
        hasAlert: true,
        alertReason: 'DESTINATÁRIO AUSENTE',
      });

      const result = await service.syncTracking(trackingCode);

      expect(result.hasAlert).toBe(true);
      expect(result.alertReason).toBe('DESTINATÁRIO AUSENTE');
      expect(mockNotificationsService.sendNotification).toHaveBeenCalled();
    });
  });

  describe('getTrackingByOrder', () => {
    it('should return tracking for an order', async () => {
      const orderId = 'order-123';
      const mockTracking = {
        id: 'tracking-123',
        orderId,
        code: 'BR123456789BR',
        status: 'Em trânsito',
        events: [],
      };

      mockPrismaService.tracking.findFirst.mockResolvedValue(mockTracking);

      const result = await service.getTrackingByOrder(orderId);

      expect(result).toEqual(mockTracking);
      expect(mockPrismaService.tracking.findFirst).toHaveBeenCalledWith({
        where: { orderId },
        include: { order: true },
      });
    });

    it('should return null if tracking not found', async () => {
      mockPrismaService.tracking.findFirst.mockResolvedValue(null);

      const result = await service.getTrackingByOrder('invalid-order');

      expect(result).toBeNull();
    });
  });

  describe('syncAllActiveTrackings', () => {
    it('should sync all active trackings', async () => {
      const activeTrackings = [
        {
          id: 'tracking-1',
          code: 'BR111111111BR',
          isDelivered: false,
        },
        {
          id: 'tracking-2',
          code: 'BR222222222BR',
          isDelivered: false,
        },
      ];

      mockPrismaService.tracking.findMany.mockResolvedValue(activeTrackings);
      
      // Mock syncTracking for each tracking
      jest.spyOn(service, 'syncTracking').mockResolvedValue({} as any);

      await service.syncAllActiveTrackings();

      expect(mockPrismaService.tracking.findMany).toHaveBeenCalledWith({
        where: {
          isDelivered: false,
          hasAlert: false,
        },
        select: {
          code: true,
        },
      });
      expect(service.syncTracking).toHaveBeenCalledTimes(2);
    });
  });

  describe('getTrackingAlerts', () => {
    it('should return tracking alerts with filters', async () => {
      const filters = {
        startDate: new Date('2024-01-01'),
        endDate: new Date('2024-01-31'),
        limit: 10,
      };

      const mockAlerts = [
        {
          id: 'tracking-1',
          code: 'BR111111111BR',
          hasAlert: true,
          alertReason: 'DESTINATÁRIO AUSENTE',
          order: {
            id: 'order-1',
            customerName: 'João Silva',
          },
        },
      ];

      mockPrismaService.tracking.findMany.mockResolvedValue(mockAlerts);

      const result = await service.getTrackingAlerts(filters);

      expect(result).toEqual(mockAlerts);
      expect(mockPrismaService.tracking.findMany).toHaveBeenCalledWith({
        where: {
          hasAlert: true,
          lastUpdate: {
            gte: filters.startDate,
            lte: filters.endDate,
          },
        },
        include: {
          order: {
            include: {
              customer: true,
              seller: true,
              collector: true,
            },
          },
        },
        orderBy: {
          lastUpdate: 'desc',
        },
        take: filters.limit,
      });
    });
  });
});