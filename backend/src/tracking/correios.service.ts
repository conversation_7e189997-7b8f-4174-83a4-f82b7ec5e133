import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import axios from 'axios';

interface CorreiosEvent {
  dtHrCriado: string;
  codigo: string;
  tipo: string;
  descricao: string;
  unidade?: {
    nome: string;
    endereco?: {
      cidade?: string;
      uf?: string;
    };
  };
  unidadeDestino?: {
    nome: string;
    endereco?: {
      cidade?: string;
      uf?: string;
    };
  };
}

interface CorreiosResponse {
  objetos: Array<{
    codObjeto: string;
    mensagem?: string;
    eventos?: CorreiosEvent[];
  }>;
}

@Injectable()
export class CorreiosService {
  private readonly apiUrl = 'https://proxyapp.correios.com.br/v1/sro-rastro';
  
  // Critical statuses that require immediate attention
  private readonly criticalStatuses = [
    'BDE', 'BDI', 'BDR',  // Delivered
    'BLQ',                // Blocked/held
    'FC',                 // Delivery failed
    'LDE',                // Awaiting pickup
    'PAR',                // Ready for pickup
    'OEC',                // Out for delivery
    'CD', 'AC', 'CA',     // Forwarding/address issues
  ];

  async track(trackingCode: string): Promise<any> {
    try {
      const response = await axios.get<CorreiosResponse>(
        `${this.apiUrl}/${trackingCode}`,
        {
          headers: {
            'User-Agent':
              'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            Accept: 'application/json',
          },
          timeout: 10000,
        },
      );

      const objeto = response.data.objetos?.[0];
      
      if (!objeto) {
        throw new Error('No tracking data found');
      }

      if (objeto.mensagem && !objeto.eventos) {
        throw new Error(objeto.mensagem);
      }

      const eventos = objeto.eventos || [];
      const ultimoEvento = eventos[0];

      const entregue = this.isDelivered(ultimoEvento);
      
      return {
        codigo: trackingCode,
        eventos: eventos.map(this.formatEvent),
        ultimaAtualizacao: ultimoEvento?.dtHrCriado,
        status: this.getStatusFromEvent(ultimoEvento),
        entregue,
      };
    } catch (error) {
      if (error.response?.status === 404) {
        throw new HttpException(
          'Tracking code not found',
          HttpStatus.NOT_FOUND,
        );
      }
      
      if (error.code === 'ECONNABORTED') {
        throw new HttpException(
          'Correios API timeout',
          HttpStatus.REQUEST_TIMEOUT,
        );
      }

      throw new HttpException(
        error.message || 'Failed to track package',
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  private formatEvent(evento: CorreiosEvent) {
    const location = evento.unidade
      ? `${evento.unidade.nome}${
          evento.unidade.endereco?.cidade
            ? ` - ${evento.unidade.endereco.cidade}/${evento.unidade.endereco.uf}`
            : ''
        }`
      : null;

    const destination = evento.unidadeDestino
      ? `${evento.unidadeDestino.nome}${
          evento.unidadeDestino.endereco?.cidade
            ? ` - ${evento.unidadeDestino.endereco.cidade}/${evento.unidadeDestino.endereco.uf}`
            : ''
        }`
      : null;

    return {
      data: evento.dtHrCriado,
      hora: new Date(evento.dtHrCriado).toLocaleTimeString('pt-BR'),
      descricao: evento.descricao,
      local: location,
      destino: destination,
      tipo: evento.tipo,
      codigo: evento.codigo,
    };
  }

  private getStatusFromEvent(evento?: CorreiosEvent): string {
    if (!evento) return 'unknown';

    const codigo = evento.codigo?.toUpperCase();
    
    // Map event codes to statuses
    const statusMap: Record<string, string> = {
      'BDE': 'delivered',
      'BDI': 'delivered',
      'BDR': 'delivered',
      'OEC': 'out_for_delivery',
      'LDI': 'in_transit',
      'RO': 'posted',
      'PO': 'posted',
      'DO': 'in_transit',
      'FC': 'delivery_failed',
      'LDE': 'awaiting_pickup',
    };

    return statusMap[codigo] || 'in_transit';
  }
  
  private isDelivered(evento?: CorreiosEvent): boolean {
    if (!evento) return false;
    const codigo = evento.codigo?.toUpperCase();
    return ['BDE', 'BDI', 'BDR'].includes(codigo);
  }
  
  isCriticalStatus(codigo: string): boolean {
    return this.criticalStatuses.includes(codigo?.toUpperCase());
  }
  
  // Mock response for testing
  getMockResponse(trackingCode: string): any {
    const mockEvents = [
      {
        dtHrCriado: new Date().toISOString(),
        codigo: 'BDE',
        tipo: 'BDE',
        descricao: 'Objeto entregue ao destinatário',
        unidade: {
          nome: 'CENTRO DE DISTRIBUIÇÃO',
          endereco: {
            cidade: 'SÃO PAULO',
            uf: 'SP',
          },
        },
      },
      {
        dtHrCriado: new Date(Date.now() - 86400000).toISOString(),
        codigo: 'OEC',
        tipo: 'OEC',
        descricao: 'Objeto saiu para entrega ao destinatário',
        unidade: {
          nome: 'CENTRO DE DISTRIBUIÇÃO',
          endereco: {
            cidade: 'SÃO PAULO',
            uf: 'SP',
          },
        },
      },
    ];
    
    return {
      codigo: trackingCode,
      eventos: mockEvents.map(this.formatEvent),
      ultimaAtualizacao: mockEvents[0].dtHrCriado,
      status: 'delivered',
      entregue: true,
    };
  }
}