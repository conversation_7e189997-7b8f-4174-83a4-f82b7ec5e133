import { Test, TestingModule } from '@nestjs/testing';
import { AuthService } from './auth.service';
import { UserService } from '../users/user.service';
import { JwtService } from '@nestjs/jwt';
import { PrismaService } from '../prisma/prisma.service';
import * as bcrypt from 'bcrypt';
import { Role } from '@prisma/client';

jest.mock('bcrypt');

const mockUserService = {};

const mockJwtService = {
  sign: jest.fn(),
};

const mockPrismaService = {
  user: {
    findFirst: jest.fn(),
  },
  tenant: {
    findFirst: jest.fn(),
  },
};

describe('AuthService', () => {
  let service: AuthService;
  let prisma: any;
  let jwtService: jest.Mocked<JwtService>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthService,
        {
          provide: UserService,
          useValue: mockUserService,
        },
        {
          provide: JwtService,
          useValue: mockJwtService,
        },
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
      ],
    }).compile();

    service = module.get<AuthService>(AuthService);
    prisma = module.get(PrismaService);
    jwtService = module.get<jest.Mocked<JwtService>>(JwtService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('validateUser', () => {
    const mockUser = {
      id: 'user-1',
      email: '<EMAIL>',
      hashedPassword: 'hashedPassword',
      fullName: 'Test User',
      role: Role.ADMIN,
      isActive: true,
      tenantId: 'tenant-1',
    };

    it('should return user without password when credentials are valid', async () => {
      prisma.user.findFirst.mockResolvedValue(mockUser);
      (bcrypt.compare as jest.Mock).mockResolvedValue(true);

      const result = await service.validateUser(
        '<EMAIL>',
        'password',
        'tenant-1',
      );

      expect(prisma.user.findFirst).toHaveBeenCalledWith({
        where: { email: '<EMAIL>', tenantId: 'tenant-1' },
      });
      expect(result).toEqual({
        id: 'user-1',
        email: '<EMAIL>',
        fullName: 'Test User',
        role: Role.ADMIN,
        isActive: true,
        tenantId: 'tenant-1',
      });
    });

    it('should return null when user is not found', async () => {
      prisma.user.findFirst.mockResolvedValue(null);

      const result = await service.validateUser(
        '<EMAIL>',
        'password',
        'tenant-1',
      );

      expect(result).toBeNull();
    });

    it('should return null when password is invalid', async () => {
      prisma.user.findFirst.mockResolvedValue(mockUser);
      (bcrypt.compare as jest.Mock).mockResolvedValue(false);

      const result = await service.validateUser(
        '<EMAIL>',
        'wrongpassword',
        'tenant-1',
      );

      expect(result).toBeNull();
    });
  });

  describe('login', () => {
    const mockUser = {
      id: 'user-1',
      email: '<EMAIL>',
      fullName: 'Test User',
      role: Role.ADMIN,
      tenantId: 'tenant-1',
    };

    it('should return access token and user info', async () => {
      const mockToken = 'jwt-token';
      jwtService.sign.mockReturnValue(mockToken);

      const result = await service.login(mockUser);

      expect(jwtService.sign).toHaveBeenCalledWith({
        email: mockUser.email,
        sub: mockUser.id,
        role: mockUser.role,
        tenantId: mockUser.tenantId,
      });

      expect(result).toEqual({
        access_token: mockToken,
        user: {
          id: mockUser.id,
          email: mockUser.email,
          role: mockUser.role,
          fullName: mockUser.fullName,
        },
      });
    });
  });

  describe('getTenantId', () => {
    it('should return tenant id for valid email domain', async () => {
      const mockTenant = { id: 'tenant-1', domain: 'example.com' };
      prisma.tenant.findFirst.mockResolvedValue(mockTenant);

      const result = await service.getTenantId('<EMAIL>');

      expect(prisma.tenant.findFirst).toHaveBeenCalledWith({
        where: { domain: 'example.com' },
      });
      expect(result).toEqual({ tenantId: 'tenant-1' });
    });

    it('should throw error when tenant not found', async () => {
      prisma.tenant.findFirst.mockResolvedValue(null);

      await expect(service.getTenantId('<EMAIL>'))
        .rejects
        .toThrow('Tenant not found for this email domain');
    });
  });
});