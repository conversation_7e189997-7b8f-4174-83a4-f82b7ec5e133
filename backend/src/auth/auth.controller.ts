import { Controller, Post, Body, Get, UseGuards, Request, HttpException, HttpStatus } from '@nestjs/common';
import { AuthService } from './auth.service';
import { JwtAuthGuard } from './jwt-auth.guard';

@Controller('auth')
export class AuthController {
  constructor(private authService: AuthService) {}

  @Post('login')
  async login(@Body() loginDto: { email: string; password: string }) {
    try {
      console.log('🔐 Login attempt for:', loginDto.email);
      const result = await this.authService.login(loginDto.email, loginDto.password);
      console.log('✅ Login successful for:', loginDto.email);
      return result;
    } catch (error) {
      console.error('❌ Login failed:', error.message);
      throw new HttpException(
        error.message || 'Erro ao fazer login',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @UseGuards(JwtAuthGuard)
  @Get('me')
  async getProfile(@Request() req) {
    return this.authService.getProfile(req.user.userId);
  }
}