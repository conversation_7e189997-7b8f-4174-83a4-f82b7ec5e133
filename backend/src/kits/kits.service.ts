import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateKitDto } from './dto/create-kit.dto';
import { UpdateKitDto } from './dto/update-kit.dto';
import { Kit, KitItem, Prisma } from '@prisma/client';

@Injectable()
export class KitsService {
  constructor(private prisma: PrismaService) {}

  async create(createKitDto: CreateKitDto, tenantId: string): Promise<Kit> {
    const { items, ...kitData } = createKitDto;

    // Valida se todos os produtos/variações existem
    await this.validateKitItems(items);

    // Cria o kit com seus itens
    return this.prisma.kit.create({
      data: {
        tenantId,
        ...kitData,
        items: {
          create: items.map(item => ({
            productVariationId: item.productVariationId,
            quantity: item.quantity,
          })),
        },
      },
      include: {
        items: {
          include: {
            productVariation: {
              include: {
                product: true,
                inventory: true,
              },
            },
          },
        },
      },
    });
  }

  async findAll(active?: boolean, tenantId?: string): Promise<Kit[]> {
    const where: Prisma.KitWhereInput = {};
    
    if (active !== undefined) {
      where.active = active;
    }
    
    if (tenantId) {
      where.tenantId = tenantId;
    }

    return this.prisma.kit.findMany({
      where,
      include: {
        items: {
          include: {
            productVariation: {
              include: {
                product: true,
                inventory: true,
              },
            },
          },
        },
      },
      orderBy: {
        name: 'asc',
      },
    });
  }

  async findOne(id: string, tenantId: string): Promise<Kit> {
    const kit = await this.prisma.kit.findFirst({
      where: { id, tenantId },
      include: {
        items: {
          include: {
            productVariation: {
              include: {
                product: true,
                inventory: true,
              },
            },
          },
        },
      },
    });

    if (!kit) {
      throw new NotFoundException('Kit não encontrado');
    }

    return kit;
  }

  async update(id: string, tenantId: string, updateKitDto: UpdateKitDto): Promise<Kit> {
    await this.findOne(id, tenantId);

    return this.prisma.kit.update({
      where: { id },
      data: updateKitDto,
      include: {
        items: {
          include: {
            productVariation: {
              include: {
                product: true,
                inventory: true,
              },
            },
          },
        },
      },
    });
  }

  async remove(id: string, tenantId: string): Promise<Kit> {
    await this.findOne(id, tenantId);

    // Soft delete - apenas desativa
    return this.prisma.kit.update({
      where: { id },
      data: {
        active: false,
      },
    });
  }

  // Adicionar itens ao kit
  async addItems(kitId: string, tenantId: string, items: { productVariationId: string; quantity: number }[]): Promise<Kit> {
    await this.findOne(kitId, tenantId);
    await this.validateKitItems(items); // Valida novos itens

    // Adiciona novos itens
    await this.prisma.kitItem.createMany({
      data: items.map(item => ({
        kitId,
        productVariationId: item.productVariationId,
        quantity: item.quantity,
      })),
    });

    return this.findOne(kitId, tenantId);
  }

  // Remover item do kit
  async removeItem(kitId: string, itemId: string, tenantId: string): Promise<Kit> {
    const kit = await this.findOne(kitId, tenantId);
    
    const kitWithItems = kit as Kit & { items: any[] };
    const item = kitWithItems.items.find(i => i.id === itemId);
    if (!item) {
      throw new NotFoundException('Item não encontrado no kit');
    }

    await this.prisma.kitItem.delete({
      where: { id: itemId },
    });

    return this.findOne(kitId, tenantId);
  }

  // Atualizar quantidade de um item no kit
  async updateItemQuantity(kitId: string, itemId: string, quantity: number, tenantId: string): Promise<Kit> {
    if (quantity < 1) {
      throw new BadRequestException('Quantidade deve ser maior que zero');
    }

    const kit = await this.findOne(kitId, tenantId);
    
    const kitWithItems = kit as Kit & { items: any[] };
    const item = kitWithItems.items.find(i => i.id === itemId);
    if (!item) {
      throw new NotFoundException('Item não encontrado no kit');
    }

    await this.prisma.kitItem.update({
      where: { id: itemId },
      data: {
        quantity,
      },
    });

    return this.findOne(kitId, tenantId);
  }

  // Verificar disponibilidade do kit
  async checkAvailability(kitId: string, requestedQuantity: number = 1, tenantId: string): Promise<{
    available: boolean;
    maxAvailable: number;
    limitingItems: Array<{
      productName: string;
      variation: string;
      required: number;
      available: number;
    }>;
  }> {
    const kit = await this.findOne(kitId, tenantId);
    
    if (!kit.active) {
      return {
        available: false,
        maxAvailable: 0,
        limitingItems: [],
      };
    }

    let maxAvailable = Number.MAX_SAFE_INTEGER;
    const limitingItems: Array<{
      productName: string;
      variation: string;
      required: number;
      available: number;
    }> = [];

    // Calcula a quantidade máxima disponível baseada no estoque de cada item
    const kitWithItems = kit as Kit & { items: any[] };
    for (const item of kitWithItems.items) {
      const inventory = item.productVariation.inventory;
      const product = item.productVariation.product;
      
      if (!inventory) {
        maxAvailable = 0;
        limitingItems.push({
          productName: product.name,
          variation: item.productVariation.variation,
          required: item.quantity * requestedQuantity,
          available: 0,
        });
        continue;
      }

      const availableForThisItem = Math.floor(inventory.quantity / item.quantity);
      
      if (availableForThisItem < maxAvailable) {
        maxAvailable = availableForThisItem;
      }

      if (inventory.quantity < item.quantity * requestedQuantity) {
        limitingItems.push({
          productName: product.name,
          variation: item.productVariation.variation,
          required: item.quantity * requestedQuantity,
          available: inventory.quantity,
        });
      }
    }

    return {
      available: maxAvailable >= requestedQuantity,
      maxAvailable,
      limitingItems,
    };
  }

  // Reservar estoque do kit (para uso em vendas)
  async reserveStock(kitId: string, quantity: number, tenantId: string): Promise<void> {
    const availability = await this.checkAvailability(kitId, quantity, tenantId);
    
    if (!availability.available) {
      throw new BadRequestException(
        `Kit não disponível. Quantidade máxima: ${availability.maxAvailable}`
      );
    }

    const kit = await this.findOne(kitId, tenantId);

    // Usa transação para garantir atomicidade
    await this.prisma.$transaction(async (tx) => {
      const kitWithItems = kit as Kit & { items: any[] };
      for (const item of kitWithItems.items) {
        const totalRequired = item.quantity * quantity;
        
        await tx.inventory.update({
          where: { productVariationId: item.productVariationId },
          data: {
            quantity: {
              decrement: totalRequired,
            },
          },
        });
      }
    });
  }

  // Liberar estoque do kit (para cancelamento de vendas)
  async releaseStock(kitId: string, quantity: number, tenantId: string): Promise<void> {
    const kit = await this.findOne(kitId, tenantId);

    // Usa transação para garantir atomicidade
    await this.prisma.$transaction(async (tx) => {
      const kitWithItems = kit as Kit & { items: any[] };
      for (const item of kitWithItems.items) {
        const totalToReturn = item.quantity * quantity;
        
        await tx.inventory.update({
          where: { productVariationId: item.productVariationId },
          data: {
            quantity: {
              increment: totalToReturn,
            },
          },
        });
      }
    });
  }

  // Validar se variações existem
  private async validateKitItems(items: { productVariationId: string; quantity: number }[]): Promise<void> {
    if (!items || items.length === 0) {
      throw new BadRequestException('Kit deve ter pelo menos um item');
    }

    const variationIds = items.map(item => item.productVariationId);
    const variations = await this.prisma.productVariation.findMany({
      where: {
        id: { in: variationIds },
      },
      include: {
        product: true,
      },
    });

    if (variations.length !== variationIds.length) {
      throw new BadRequestException('Uma ou mais variações não foram encontradas');
    }

    // Verifica se há produtos inativos
    const inactiveProducts = variations.filter(v => !v.active || !v.product.active);
    if (inactiveProducts.length > 0) {
      throw new BadRequestException(
        `Produtos inativos: ${inactiveProducts.map(v => v.product.name).join(', ')}`
      );
    }
  }
}