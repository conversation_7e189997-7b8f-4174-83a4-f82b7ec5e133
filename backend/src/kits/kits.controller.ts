import {
  Controller,
  Get,
  Post,
  Patch,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  ValidationPipe,
  UsePipes,
  ParseBoolPipe,
  ParseIntPipe,
} from '@nestjs/common';
import { KitsService } from './kits.service';
import { CreateKitDto } from './dto/create-kit.dto';
import { UpdateKitDto } from './dto/update-kit.dto';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { RolesGuard } from '../auth/roles.guard';
import { Roles } from '../auth/roles.decorator';
import { TenantId } from '../common/decorators/tenant.decorator';

@Controller('kits')
@UseGuards(JwtAuthGuard, RolesGuard)
export class KitsController {
  constructor(private readonly kitsService: KitsService) {}

  // CRUD básico de Kits
  @Post()
  @Roles('ADMIN', 'SUPERVISOR')
  @UsePipes(new ValidationPipe({ whitelist: true }))
  async create(
    @Body() createKitDto: CreateKitDto,
    @TenantId() tenantId: string,
  ) {
    return this.kitsService.create(createKitDto, tenantId);
  }

  @Get()
  @Roles('ADMIN', 'SUPERVISOR', 'VENDEDOR', 'COBRADOR')
  async findAll(
    @TenantId() tenantId: string,
    @Query('active', new ParseBoolPipe({ optional: true })) active?: boolean,
  ) {
    return this.kitsService.findAll(active, tenantId);
  }

  @Get(':id')
  @Roles('ADMIN', 'SUPERVISOR', 'VENDEDOR', 'COBRADOR')
  async findOne(
    @Param('id') id: string,
    @TenantId() tenantId: string,
  ) {
    return this.kitsService.findOne(id, tenantId);
  }

  @Patch(':id')
  @Roles('ADMIN', 'SUPERVISOR')
  @UsePipes(new ValidationPipe({ whitelist: true }))
  async update(
    @Param('id') id: string,
    @Body() updateKitDto: UpdateKitDto,
    @TenantId() tenantId: string,
  ) {
    return this.kitsService.update(id, tenantId, updateKitDto);
  }

  @Delete(':id')
  @Roles('ADMIN', 'SUPERVISOR')
  async remove(
    @Param('id') id: string,
    @TenantId() tenantId: string,
  ) {
    return this.kitsService.remove(id, tenantId);
  }

  // Gerenciamento de itens do kit
  @Post(':id/items')
  @Roles('ADMIN', 'SUPERVISOR')
  async addItems(
    @Param('id') kitId: string,
    @Body() items: { productVariationId: string; quantity: number }[],
    @TenantId() tenantId: string,
  ) {
    return this.kitsService.addItems(kitId, tenantId, items);
  }

  @Delete(':id/items/:itemId')
  @Roles('ADMIN', 'SUPERVISOR')
  async removeItem(
    @Param('id') kitId: string,
    @Param('itemId') itemId: string,
    @TenantId() tenantId: string,
  ) {
    return this.kitsService.removeItem(kitId, itemId, tenantId);
  }

  @Patch(':id/items/:itemId')
  @Roles('ADMIN', 'SUPERVISOR')
  async updateItemQuantity(
    @Param('id') kitId: string,
    @Param('itemId') itemId: string,
    @Body('quantity', ParseIntPipe) quantity: number,
    @TenantId() tenantId: string,
  ) {
    return this.kitsService.updateItemQuantity(kitId, itemId, quantity, tenantId);
  }

  // Disponibilidade e estoque
  @Get(':id/availability')
  @Roles('ADMIN', 'SUPERVISOR', 'VENDEDOR')
  async checkAvailability(
    @Param('id') kitId: string,
    @TenantId() tenantId: string,
    @Query('quantity', new ParseIntPipe({ optional: true })) quantity?: number,
  ) {
    return this.kitsService.checkAvailability(kitId, quantity, tenantId);
  }

  @Post(':id/reserve')
  @Roles('ADMIN', 'SUPERVISOR', 'VENDEDOR')
  async reserveStock(
    @Param('id') kitId: string,
    @Body('quantity', ParseIntPipe) quantity: number,
    @TenantId() tenantId: string,
  ) {
    await this.kitsService.reserveStock(kitId, quantity, tenantId);
    return { message: 'Estoque reservado com sucesso' };
  }

  @Post(':id/release')
  @Roles('ADMIN', 'SUPERVISOR')
  async releaseStock(
    @Param('id') kitId: string,
    @Body('quantity', ParseIntPipe) quantity: number,
    @TenantId() tenantId: string,
  ) {
    await this.kitsService.releaseStock(kitId, quantity, tenantId);
    return { message: 'Estoque liberado com sucesso' };
  }
}