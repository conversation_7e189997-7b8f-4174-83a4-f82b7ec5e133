import { Test, TestingModule } from '@nestjs/testing';
import { ReportsService } from './reports.service';
import { PrismaService } from '../prisma/prisma.service';
import { OrderStatus, Role } from '@prisma/client';
import { Decimal } from '@prisma/client/runtime/library';

describe('ReportsService', () => {
  let service: ReportsService;
  let prisma: PrismaService;

  const mockPrismaService = {
    order: {
      count: jest.fn(),
      findMany: jest.fn(),
      groupBy: jest.fn(),
      aggregate: jest.fn(),
    },
    orderItem: {
      findMany: jest.fn(),
    },
    user: {
      findUnique: jest.fn(),
    },
    tracking: {
      findMany: jest.fn(),
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ReportsService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
      ],
    }).compile();

    service = module.get<ReportsService>(ReportsService);
    prisma = module.get<PrismaService>(PrismaService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getSummary', () => {
    it('should return summary report with all metrics', async () => {
      // Mock contagens
      mockPrismaService.order.count
        .mockResolvedValueOnce(100) // total
        .mockResolvedValueOnce(60)  // delivered
        .mockResolvedValueOnce(20)  // pending
        .mockResolvedValueOnce(5)   // failed
        .mockResolvedValueOnce(15); // in transit

      // Mock agregações
      mockPrismaService.order.aggregate.mockResolvedValue({
        _sum: { total: new Decimal(10000) },
        _avg: { total: new Decimal(100) },
      });

      const result = await service.getSummary({});

      expect(result).toEqual({
        totalOrders: 100,
        totalRevenue: new Decimal(10000),
        totalDelivered: 60,
        totalPending: 20,
        totalFailed: 5,
        totalInTransit: 15,
        averageOrderValue: new Decimal(100),
        periodStart: undefined,
        periodEnd: undefined,
      });
    });

    it('should apply date filters', async () => {
      const startDate = '2024-01-01';
      const endDate = '2024-01-31';

      mockPrismaService.order.count.mockResolvedValue(0);
      mockPrismaService.order.aggregate.mockResolvedValue({
        _sum: { total: null },
        _avg: { total: null },
      });

      const result = await service.getSummary({ startDate, endDate });

      expect(mockPrismaService.order.count).toHaveBeenCalledWith({
        where: expect.objectContaining({
          createdAt: {
            gte: new Date(startDate),
            lte: new Date(endDate),
          },
        }),
      });

      expect(result.periodStart).toEqual(new Date(startDate));
      expect(result.periodEnd).toEqual(new Date(endDate));
    });

    it('should respect role restrictions for VENDEDOR', async () => {
      const userId = 'seller-123';
      const userRole = Role.VENDEDOR;

      mockPrismaService.order.count.mockResolvedValue(0);
      mockPrismaService.order.aggregate.mockResolvedValue({
        _sum: { total: null },
        _avg: { total: null },
      });

      await service.getSummary({}, userId, userRole);

      expect(mockPrismaService.order.count).toHaveBeenCalledWith({
        where: expect.objectContaining({
          sellerId: userId,
        }),
      });
    });
  });

  describe('getByStatus', () => {
    it('should return orders grouped by status with percentages', async () => {
      const mockStatusGroups = [
        {
          status: OrderStatus.ENTREGUE,
          _count: { _all: 60 },
          _sum: { total: new Decimal(6000) },
        },
        {
          status: OrderStatus.PENDENTE,
          _count: { _all: 30 },
          _sum: { total: new Decimal(3000) },
        },
        {
          status: OrderStatus.FALHA,
          _count: { _all: 10 },
          _sum: { total: new Decimal(1000) },
        },
      ];

      mockPrismaService.order.groupBy.mockResolvedValue(mockStatusGroups);

      const result = await service.getByStatus({});

      expect(result).toHaveLength(3);
      expect(result[0]).toEqual({
        status: OrderStatus.ENTREGUE,
        count: 60,
        totalRevenue: new Decimal(6000),
        percentage: 60, // 60/100 * 100
      });
      expect(result[1].percentage).toBe(30);
      expect(result[2].percentage).toBe(10);
    });
  });

  describe('getByProduct', () => {
    it('should aggregate order items by product', async () => {
      const mockOrderItems = [
        {
          productId: 'prod-1',
          productName: 'Camiseta',
          quantity: 5,
          unitPrice: new Decimal(30),
          orderId: 'order-1',
          order: {},
        },
        {
          productId: 'prod-1',
          productName: 'Camiseta',
          quantity: 3,
          unitPrice: new Decimal(30),
          orderId: 'order-2',
          order: {},
        },
        {
          productId: 'prod-2',
          productName: 'Calça',
          quantity: 2,
          unitPrice: new Decimal(90),
          orderId: 'order-1',
          order: {},
        },
      ];

      mockPrismaService.orderItem.findMany.mockResolvedValue(mockOrderItems);

      const result = await service.getByProduct({});

      expect(result).toHaveLength(2);
      
      const camiseta = result.find(p => p.productId === 'prod-1');
      expect(camiseta).toEqual({
        productId: 'prod-1',
        productName: 'Camiseta',
        totalQuantity: 8,
        totalRevenue: new Decimal(240), // 8 * 30
        orderCount: 2,
        averagePrice: new Decimal(30),
      });

      const calca = result.find(p => p.productId === 'prod-2');
      expect(calca).toEqual({
        productId: 'prod-2',
        productName: 'Calça',
        totalQuantity: 2,
        totalRevenue: new Decimal(180), // 2 * 90
        orderCount: 1,
        averagePrice: new Decimal(90),
      });
    });
  });

  describe('getTrackingAlerts', () => {
    it('should return tracking alerts with customer info', async () => {
      const mockTrackings = [
        {
          id: 'track-1',
          orderId: 'order-1',
          code: 'AB123456789BR',
          status: 'DESTINATÁRIO AUSENTE',
          hasAlert: true,
          alertReason: 'DESTINATÁRIO AUSENTE',
          lastUpdate: new Date('2024-01-15'),
          order: {
            id: 'order-1',
            customerName: 'João Silva',
            customerPhone: '11999999999',
            customer: {
              name: 'João Silva',
              phone: '11999999999',
            },
          },
        },
      ];

      mockPrismaService.tracking.findMany.mockResolvedValue(mockTrackings);

      const result = await service.getTrackingAlerts({});

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        orderId: 'order-1',
        trackingCode: 'AB123456789BR',
        status: 'DESTINATÁRIO AUSENTE',
        alertReason: 'DESTINATÁRIO AUSENTE',
        customerName: 'João Silva',
        customerPhone: '11999999999',
        lastUpdate: new Date('2024-01-15'),
      });
    });
  });

  describe('Performance calculations', () => {
    it('should calculate conversion rate correctly', async () => {
      // Mock para calculateConversionRate
      mockPrismaService.order.count
        .mockResolvedValueOnce(100) // total orders
        .mockResolvedValueOnce(75); // delivered orders

      mockPrismaService.order.findMany.mockResolvedValue([]);
      mockPrismaService.order.aggregate.mockResolvedValue({
        _sum: { total: null },
        _avg: { total: null },
      });

      const stats = await service.getPerformanceStats({});

      expect(stats.conversionRate).toBe(75); // 75/100 * 100
    });

    it('should handle zero orders in conversion rate', async () => {
      mockPrismaService.order.count.mockResolvedValue(0);
      mockPrismaService.order.findMany.mockResolvedValue([]);
      mockPrismaService.order.aggregate.mockResolvedValue({
        _sum: { total: null },
        _avg: { total: null },
      });

      const stats = await service.getPerformanceStats({});

      expect(stats.conversionRate).toBe(0);
      expect(stats.returnRate).toBe(0);
    });
  });
});