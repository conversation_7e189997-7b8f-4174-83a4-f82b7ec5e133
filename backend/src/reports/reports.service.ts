import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { FilterReportDto } from './dto/filter-report.dto';
import { Prisma, OrderStatus, Role } from '@prisma/client';
import { Decimal } from '@prisma/client/runtime/library';

export interface SummaryReport {
  totalOrders: number;
  totalRevenue: Decimal;
  totalDelivered: number;
  totalPending: number;
  totalFailed: number;
  totalInTransit: number;
  averageOrderValue: Decimal;
  periodStart?: Date;
  periodEnd?: Date;
}

export interface CollectorReport {
  collectorId: string;
  collectorName: string;
  totalOrders: number;
  totalRevenue: Decimal;
  completedOrders: number;
  pendingOrders: number;
  failedOrders: number;
  averageOrderValue: Decimal;
}

export interface SellerReport {
  sellerId: string;
  sellerName: string;
  totalOrders: number;
  totalRevenue: Decimal;
  completedOrders: number;
  pendingOrders: number;
  cancelledOrders: number;
  averageOrderValue: Decimal;
}

export interface StatusReport {
  status: OrderStatus;
  count: number;
  totalRevenue: Decimal;
  percentage: number;
}

export interface ProductReport {
  productId: string;
  productName: string;
  totalQuantity: number;
  totalRevenue: Decimal;
  orderCount: number;
  averagePrice: Decimal;
}

export interface TrackingAlert {
  orderId: string;
  trackingCode: string;
  status: string;
  alertReason: string | null;
  customerName: string;
  customerPhone: string;
  lastUpdate: Date;
}

@Injectable()
export class ReportsService {
  constructor(private prisma: PrismaService) {}

  /**
   * Constrói WHERE clause baseado nos filtros
   * Os filtros sellerId e collectorId já vêm processados pelo interceptor
   */
  private buildWhereClause(filters: FilterReportDto): Prisma.OrderWhereInput {
    const where: Prisma.OrderWhereInput = {};

    // Filtros de data - usa paymentReceivedDate ao invés de createdAt
    if (filters.startDate || filters.endDate) {
      where.paymentReceivedDate = {};
      if (filters.startDate) {
        where.paymentReceivedDate.gte = new Date(filters.startDate);
      }
      if (filters.endDate) {
        where.paymentReceivedDate.lte = new Date(filters.endDate);
      }
    }

    // Filtros específicos - já processados pelo interceptor
    if (filters.sellerId) {
      where.sellerId = filters.sellerId;
    }
    if (filters.collectorId) {
      where.collectorId = filters.collectorId;
    }
    if (filters.status) {
      where.status = filters.status;
    }
    if (filters.customerId) {
      where.customerId = filters.customerId;
    }

    return where;
  }

  /**
   * Relatório resumo geral
   */
  async getSummary(filters: FilterReportDto): Promise<SummaryReport> {
    const where = this.buildWhereClause(filters);

    // Contagens por status
    const [
      totalOrders,
      totalDelivered,
      totalPending,
      totalFailed,
      totalInTransit,
      revenueData,
    ] = await Promise.all([
      this.prisma.order.count({ where }),
      this.prisma.order.count({ where: { ...where, status: OrderStatus.Completo } }),
      this.prisma.order.count({ where: { ...where, status: { in: [OrderStatus.Analise, OrderStatus.PagamentoPendente] } } }),
      this.prisma.order.count({ where: { ...where, status: { in: [OrderStatus.Frustrado, OrderStatus.EntregaFalha, OrderStatus.DevolvidoCorreios] } } }),
      this.prisma.order.count({ 
        where: { 
          ...where, 
          status: { 
            in: [OrderStatus.Transito, OrderStatus.Separacao, OrderStatus.Recuperacao, OrderStatus.ConfirmarEntrega] 
          } 
        } 
      }),
      this.prisma.order.aggregate({
        where,
        _sum: { total: true },
        _avg: { total: true },
      }),
    ]);

    return {
      totalOrders,
      totalRevenue: revenueData._sum.total || new Decimal(0),
      totalDelivered,
      totalPending,
      totalFailed,
      totalInTransit,
      averageOrderValue: revenueData._avg.total || new Decimal(0),
      periodStart: filters.startDate ? new Date(filters.startDate) : undefined,
      periodEnd: filters.endDate ? new Date(filters.endDate) : undefined,
    };
  }

  /**
   * Relatório por cobrador
   */
  async getByCollector(filters: FilterReportDto): Promise<CollectorReport[]> {
    const where = this.buildWhereClause(filters);
    
    // Garante que apenas pedidos com cobrador sejam considerados
    where.collectorId = { not: null };

    const collectors = await this.prisma.order.groupBy({
      by: ['collectorId'],
      where,
      _count: { _all: true },
      _sum: { total: true },
    });

    const results: CollectorReport[] = [];

    for (const collector of collectors) {
      if (!collector.collectorId) continue;

      // Busca dados do cobrador
      const user = await this.prisma.user.findUnique({
        where: { id: collector.collectorId },
        select: { id: true, name: true },
      });

      if (!user) continue;

      // Busca contagens específicas por status
      const [completed, pending, failed] = await Promise.all([
        this.prisma.order.count({
          where: {
            ...where,
            collectorId: collector.collectorId,
            status: OrderStatus.Completo,
          },
        }),
        this.prisma.order.count({
          where: {
            ...where,
            collectorId: collector.collectorId,
            status: { in: [OrderStatus.Analise, OrderStatus.PagamentoPendente, OrderStatus.Negociacao] },
          },
        }),
        this.prisma.order.count({
          where: {
            ...where,
            collectorId: collector.collectorId,
            status: { in: [OrderStatus.Frustrado, OrderStatus.EntregaFalha] },
          },
        }),
      ]);

      const totalRevenue = collector._sum.total || new Decimal(0);
      const totalOrders = collector._count._all;

      results.push({
        collectorId: user.id,
        collectorName: user.name,
        totalOrders,
        totalRevenue,
        completedOrders: completed,
        pendingOrders: pending,
        failedOrders: failed,
        averageOrderValue: totalOrders > 0 
          ? totalRevenue.div(totalOrders) 
          : new Decimal(0),
      });
    }

    return results.sort((a, b) => b.totalRevenue.comparedTo(a.totalRevenue));
  }

  /**
   * Relatório por vendedor
   */
  async getBySeller(filters: FilterReportDto): Promise<SellerReport[]> {
    const where = this.buildWhereClause(filters);

    const sellers = await this.prisma.order.groupBy({
      by: ['sellerId'],
      where,
      _count: { _all: true },
      _sum: { total: true },
    });

    const results: SellerReport[] = [];

    for (const seller of sellers) {
      // Busca dados do vendedor
      const user = await this.prisma.user.findUnique({
        where: { id: seller.sellerId },
        select: { id: true, name: true },
      });

      if (!user) continue;

      // Busca contagens específicas por status
      const [completed, pending, cancelled] = await Promise.all([
        this.prisma.order.count({
          where: {
            ...where,
            sellerId: seller.sellerId,
            status: OrderStatus.Completo,
          },
        }),
        this.prisma.order.count({
          where: {
            ...where,
            sellerId: seller.sellerId,
            status: { in: [OrderStatus.Analise, OrderStatus.Separacao, OrderStatus.Transito] },
          },
        }),
        this.prisma.order.count({
          where: {
            ...where,
            sellerId: seller.sellerId,
            status: OrderStatus.Cancelado,
          },
        }),
      ]);

      const totalRevenue = seller._sum.total || new Decimal(0);
      const totalOrders = seller._count._all;

      results.push({
        sellerId: user.id,
        sellerName: user.name,
        totalOrders,
        totalRevenue,
        completedOrders: completed,
        pendingOrders: pending,
        cancelledOrders: cancelled,
        averageOrderValue: totalOrders > 0 
          ? totalRevenue.div(totalOrders) 
          : new Decimal(0),
      });
    }

    return results.sort((a, b) => b.totalRevenue.comparedTo(a.totalRevenue));
  }

  /**
   * Relatório por status
   */
  async getByStatus(filters: FilterReportDto): Promise<StatusReport[]> {
    const where = this.buildWhereClause(filters);

    const statusGroups = await this.prisma.order.groupBy({
      by: ['status'],
      where,
      _count: { _all: true },
      _sum: { total: true },
    });

    const totalOrders = statusGroups.reduce((sum, group) => sum + group._count._all, 0);

    return statusGroups.map(group => ({
      status: group.status,
      count: group._count._all,
      totalRevenue: group._sum.total || new Decimal(0),
      percentage: totalOrders > 0 ? (group._count._all / totalOrders) * 100 : 0,
    })).sort((a, b) => b.count - a.count);
  }

  /**
   * Relatório por produto
   */
  async getByProduct(filters: FilterReportDto): Promise<ProductReport[]> {
    const where = this.buildWhereClause(filters);

    // Busca itens de pedidos com filtros aplicados
    const orderItems = await this.prisma.orderItem.findMany({
      where: {
        order: where,
        ...(filters.productId && { productId: filters.productId }),
      },
      include: {
        order: true,
      },
    });

    // Agrupa por produto
    const productMap = new Map<string, {
      productName: string;
      totalQuantity: number;
      totalRevenue: Decimal;
      orderIds: Set<string>;
    }>();

    for (const item of orderItems) {
      const key = item.productId;
      const existing = productMap.get(key);

      if (existing) {
        existing.totalQuantity += item.quantity;
        existing.totalRevenue = existing.totalRevenue.add(
          item.unitPrice.mul(item.quantity)
        );
        existing.orderIds.add(item.orderId);
      } else {
        productMap.set(key, {
          productName: item.productName,
          totalQuantity: item.quantity,
          totalRevenue: item.unitPrice.mul(item.quantity),
          orderIds: new Set([item.orderId]),
        });
      }
    }

    // Converte para array de resultados
    const results: ProductReport[] = [];
    
    for (const [productId, data] of productMap) {
      results.push({
        productId,
        productName: data.productName,
        totalQuantity: data.totalQuantity,
        totalRevenue: data.totalRevenue,
        orderCount: data.orderIds.size,
        averagePrice: data.totalQuantity > 0 
          ? data.totalRevenue.div(data.totalQuantity)
          : new Decimal(0),
      });
    }

    return results.sort((a, b) => b.totalRevenue.comparedTo(a.totalRevenue));
  }

  /**
   * Alertas de rastreamento
   */
  async getTrackingAlerts(filters: FilterReportDto): Promise<TrackingAlert[]> {
    const where = this.buildWhereClause(filters);

    const trackings = await this.prisma.tracking.findMany({
      where: {
        hasAlert: true,
        order: where,
        ...(filters.hasAlert !== undefined && { hasAlert: filters.hasAlert }),
      },
      include: {
        order: {
          include: {
            customer: true,
          },
        },
      },
      orderBy: {
        lastUpdate: 'desc',
      },
    });

    return trackings.map(tracking => ({
      orderId: tracking.orderId,
      trackingCode: tracking.code,
      status: tracking.status,
      alertReason: tracking.alertReason,
      customerName: tracking.order.customer?.name || tracking.order.customerName,
      customerPhone: tracking.order.customer?.phone || tracking.order.customerPhone,
      lastUpdate: tracking.lastUpdate,
    }));
  }

  /**
   * Histórico completo de pedidos
   */
  async getHistory(filters: FilterReportDto) {
    const where = this.buildWhereClause(filters);

    const orders = await this.prisma.order.findMany({
      where,
      include: {
        customer: true,
        seller: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        collector: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        items: true,
        tracking: true,
        statusHistory: {
          include: {
            changedBy: {
              select: {
                id: true,
                name: true,
              },
            },
          },
          orderBy: {
            changedAt: 'desc',
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
      take: 1000, // Limita para evitar sobrecarga
    });

    return orders.map(order => ({
      id: order.id,
      customerName: order.customer?.name || order.customerName,
      customerPhone: order.customer?.phone || order.customerPhone,
      total: order.total,
      status: order.status,
      seller: order.seller,
      collector: order.collector,
      trackingCode: order.tracking?.code,
      hasAlert: order.tracking?.hasAlert || false,
      createdAt: order.createdAt,
      updatedAt: order.updatedAt,
      items: order.items.map(item => ({
        productName: item.productName,
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        total: item.unitPrice.mul(item.quantity),
      })),
      statusHistory: order.statusHistory,
    }));
  }

  /**
   * Estatísticas de performance por período
   */
  async getPerformanceStats(filters: FilterReportDto) {
    const where = this.buildWhereClause(filters);

    // Calcula métricas de performance
    const [
      avgDeliveryTime,
      conversionRate,
      returnRate,
    ] = await Promise.all([
      this.calculateAverageDeliveryTime(where),
      this.calculateConversionRate(where),
      this.calculateReturnRate(where),
    ]);

    return {
      averageDeliveryTime: avgDeliveryTime,
      conversionRate,
      returnRate,
      period: {
        start: filters.startDate,
        end: filters.endDate,
      },
    };
  }

  /**
   * Calcula tempo médio de entrega
   */
  private async calculateAverageDeliveryTime(where: Prisma.OrderWhereInput): Promise<number> {
    const deliveredOrders = await this.prisma.order.findMany({
      where: {
        ...where,
        status: OrderStatus.Completo,
      },
      select: {
        createdAt: true,
        updatedAt: true,
      },
    });

    if (deliveredOrders.length === 0) return 0;

    const totalDays = deliveredOrders.reduce((sum, order) => {
      const days = Math.ceil(
        (order.updatedAt.getTime() - order.createdAt.getTime()) / (1000 * 60 * 60 * 24)
      );
      return sum + days;
    }, 0);

    return Math.round(totalDays / deliveredOrders.length);
  }

  /**
   * Calcula taxa de conversão (pedidos entregues / total)
   */
  private async calculateConversionRate(where: Prisma.OrderWhereInput): Promise<number> {
    const [total, delivered] = await Promise.all([
      this.prisma.order.count({ where }),
      this.prisma.order.count({ where: { ...where, status: OrderStatus.Completo } }),
    ]);

    return total > 0 ? (delivered / total) * 100 : 0;
  }

  /**
   * Calcula taxa de devolução
   */
  private async calculateReturnRate(where: Prisma.OrderWhereInput): Promise<number> {
    const [total, failed] = await Promise.all([
      this.prisma.order.count({ where }),
      this.prisma.order.count({ 
        where: { 
          ...where, 
          status: { in: [OrderStatus.Frustrado, OrderStatus.Cancelado] } 
        } 
      }),
    ]);

    return total > 0 ? (failed / total) * 100 : 0;
  }
}