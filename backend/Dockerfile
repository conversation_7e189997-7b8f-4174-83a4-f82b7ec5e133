FROM node:20-alpine

WORKDIR /app

# Install dependencies
RUN apk add --no-cache openssl

# Copy package files and prisma
COPY package*.json ./
COPY prisma ./prisma/

# Install ALL dependencies (needed for build)
RUN npm ci

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Remove dev dependencies after build
RUN npm prune --production

# The app will use PORT env var from Railway
EXPOSE 3000

# Start the application
CMD ["node", "dist/main.js"]