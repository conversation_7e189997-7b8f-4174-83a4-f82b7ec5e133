const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function checkUsers() {
  try {
    const users = await prisma.user.findMany({
      where: { active: true },
      select: {
        id: true,
        email: true,
        role: true,
        name: true
      }
    });
    
    console.log('Usuários ativos no banco:');
    users.forEach(user => {
      console.log(`- ${user.role}: ${user.email} (${user.id})`);
    });
    
  } catch (error) {
    console.error('Erro:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkUsers();