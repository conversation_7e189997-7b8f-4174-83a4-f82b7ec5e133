import { PrismaClient, Role } from '@prisma/client';
import * as bcrypt from 'bcrypt';

const prisma = new PrismaClient({
  log: ['query', 'info', 'warn', 'error'],
});

async function createAdmin() {
  try {
    console.log('🚀 Force creating admin user...');
    
    // First, check if user exists
    const existingUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });
    
    if (existingUser) {
      console.log('⚠️  User already exists, updating...');
      
      const hashedPassword = await bcrypt.hash('admin123', 10);
      
      const updatedUser = await prisma.user.update({
        where: { email: '<EMAIL>' },
        data: {
          name: '<PERSON><PERSON>',
          password: hashedPassword,
          role: Role.ADMIN,
          active: true,
        }
      });
      
      console.log('✅ Admin user updated:');
      console.log('   ID:', updatedUser.id);
      console.log('   Name:', updatedUser.name);
      console.log('   Email:', updatedUser.email);
      console.log('   Role:', updatedUser.role);
    } else {
      console.log('📝 Creating new admin user...');
      
      const hashedPassword = await bcrypt.hash('admin123', 10);
      
      const newUser = await prisma.user.create({
        data: {
          name: 'Gilson',
          email: '<EMAIL>',
          password: hashedPassword,
          role: Role.ADMIN,
          active: true,
        }
      });
      
      console.log('✅ Admin user created:');
      console.log('   ID:', newUser.id);
      console.log('   Name:', newUser.name);
      console.log('   Email:', newUser.email);
      console.log('   Role:', newUser.role);
    }
    
    // Verify the user was created
    const allUsers = await prisma.user.findMany();
    console.log(`\n📊 Total users in database: ${allUsers.length}`);
    console.log('All users:');
    allUsers.forEach(user => {
      console.log(`   - ${user.email} (${user.role})`);
    });
    
    console.log('\n✅ Admin user is ready!');
    console.log('   📧 Email: <EMAIL>');
    console.log('   🔑 Password: admin123');
    
  } catch (error) {
    console.error('❌ Error creating admin user:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
createAdmin()
  .then(() => {
    console.log('✅ Script completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Script failed:', error);
    process.exit(1);
  });