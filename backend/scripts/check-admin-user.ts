import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function checkAdminUser() {
  try {
    console.log('🔍 Checking for admin user...');
    
    const admin = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });
    
    if (admin) {
      console.log('✅ Admin user exists:');
      console.log('   Name:', admin.name);
      console.log('   Email:', admin.email);
      console.log('   Role:', admin.role);
      console.log('   Active:', admin.active);
    } else {
      console.log('❌ Admin user not found');
      console.log('   Run: npx prisma db seed');
    }
    
    // Check total users
    const userCount = await prisma.user.count();
    console.log(`\n📊 Total users in database: ${userCount}`);
    
  } catch (error) {
    console.error('❌ Error checking admin user:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkAdminUser();