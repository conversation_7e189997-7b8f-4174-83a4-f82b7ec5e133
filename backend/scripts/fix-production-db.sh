#!/bin/bash
set -e

echo "🚨 Production Database Recovery Script"
echo "======================================"
echo "⚠️  WARNING: This script will reset your database schema!"
echo "⚠️  All data will be preserved if tables exist, but the migration history will be reset."
echo ""
read -p "Are you sure you want to continue? (yes/no): " confirm

if [ "$confirm" != "yes" ]; then
    echo "❌ Operation cancelled"
    exit 0
fi

echo ""
echo "🔧 Starting database recovery..."

# Step 1: Force reset the schema using db push
echo "📦 Recreating database schema from schema.prisma..."
npx prisma db push --force-reset --skip-generate

echo "✅ Database schema recreated"

# Step 2: Generate Prisma Client
echo "🏗️  Generating Prisma Client..."
npx prisma generate

# Step 3: Seed the database
echo "🌱 Seeding database with admin user..."
npx prisma db seed

echo ""
echo "✅ Database recovery completed!"
echo ""
echo "📋 Next steps:"
echo "1. Test your API health endpoint: /api/v1/health"
echo "2. Verify admin user can login: <EMAIL> / admin123"
echo "3. Commit and push these changes to trigger Railway deployment"
echo ""
echo "🎉 Your database should now be functional!"