import { PrismaClient, UserRole } from '@prisma/client';
import * as bcrypt from 'bcrypt';

const prisma = new PrismaClient();

async function updateAdminUser() {
  console.log('🔄 Updating admin user...');

  try {
    // Hash the new password
    const newPassword = await bcrypt.hash('Gilson123$', 10);

    // First, delete all existing admin users
    const deletedAdmins = await prisma.user.deleteMany({
      where: {
        role: UserRole.ADMIN,
      },
    });
    console.log(`🗑️  Deleted ${deletedAdmins.count} existing admin users`);

    // Create or get Wolf tenant
    const wolfTenant = await prisma.tenant.upsert({
      where: { domain: 'wolf.com' },
      update: {},
      create: {
        name: 'Wolf Corporation',
        domain: 'wolf.com',
      },
    });
    console.log('✅ Tenant ready:', wolfTenant.name);

    // Create new admin user
    const adminUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        hashedPassword: newPassword,
        fullName: 'Administrator',
        role: UserRole.ADMIN,
        tenantId: wolfTenant.id,
      },
    });

    console.log('✅ Admin user created successfully:');
    console.log('   Email: <EMAIL>');
    console.log('   Password: Gilson123$');
    console.log('   Tenant:', wolfTenant.name);

  } catch (error) {
    console.error('❌ Error updating admin user:', error);
    throw error;
  }
}

updateAdminUser()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });