# Railway Deployment Checklist

## Pre-Deployment Checklist
- [x] Backend builds successfully locally
- [x] railway.json configured
- [x] Health check endpoint exists at /api/v1/health
- [x] CORS configured to accept frontend URL
- [x] Prisma migrations ready
- [x] Environment variables documented

## Deployment Steps

### 1. Install Railway CLI (if not installed)
```bash
npm install -g @railway/cli
```

### 2. Login to Railway
```bash
railway login
```

### 3. Link to Existing Project
Since you already have a PostgreSQL database on Railway:
```bash
# Get the project ID from your Railway dashboard
railway link [YOUR_PROJECT_ID]
```

### 4. Set Environment Variables
```bash
# Database (already provided)
railway variables set DATABASE_URL="postgresql://postgres:<EMAIL>:5432/railway"

# Security
railway variables set JWT_SECRET="your-super-secret-jwt-key-here"

# API Configuration
railway variables set API_PREFIX="api/v1"
railway variables set NODE_ENV="production"
railway variables set PORT="3000"

# CORS
railway variables set CORS_ORIGIN="https://zencash-sand.vercel.app"
```

### 5. Deploy
```bash
railway up
```

### 6. Get Your Backend URL
```bash
railway open
```

## Post-Deployment

### 1. Update Vercel Environment Variables
Add these to your Vercel project:
```
REACT_APP_API_URL=https://[your-railway-url]/api/v1
REACT_APP_TENANT_ID=************************************
```

### 2. Redeploy Frontend on Vercel
The frontend will automatically redeploy when you update environment variables.

### 3. Test Endpoints

#### Health Check:
```bash
curl https://[your-railway-url]/api/v1/health
```

#### Swagger Docs:
Open in browser: `https://[your-railway-url]/docs`

#### Login Test:
```bash
curl -X POST https://[your-railway-url]/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -H "x-tenant-id: ************************************" \
  -d '{"email":"<EMAIL>","password":"Gilson123$"}'
```

### 4. Monitor
```bash
# View logs
railway logs

# View logs with tail
railway logs -f
```

## Expected Backend URL Format
Your backend URL will be something like:
- `https://zencash-backend-production.up.railway.app`

The full API URL for frontend will be:
- `https://zencash-backend-production.up.railway.app/api/v1`

## Troubleshooting

### If deployment fails:
1. Check logs: `railway logs`
2. Ensure all environment variables are set
3. Verify database connection
4. Check if port 3000 is being used

### If CORS errors occur:
1. Verify CORS_ORIGIN matches exactly
2. Check browser console for specific errors
3. Ensure credentials are included in requests

### If login fails:
1. Check if tenant ID is correct
2. Verify JWT_SECRET is set
3. Ensure database has the admin user
4. Check if migrations ran successfully