{"permissions": {"allow": ["Bash(find:*)", "Bash(npm install:*)", "<PERSON><PERSON>(npx husky init:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(chmod:*)", "<PERSON><PERSON>(docker-compose up:*)", "<PERSON><PERSON>(docker compose:*)", "Bash(export:*)", "Bash(ls:*)", "Bash(/usr/local/bin/docker compose up -d)", "mcp__ide__getDiagnostics", "Bash(npm run lint:*)", "Bash(cp:*)", "Bash(npm run:*)", "<PERSON><PERSON>(docker:*)", "<PERSON><PERSON>(mysql:*)", "Bash(PORT=3001 npm start)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(pkill:*)", "Bash(rm:*)", "Bash(grep:*)", "Bash(npm start)", "Bash(kill:*)", "Bash(npx serve:*)", "Bash(PORT=3002 npm start)", "Bash(rg:*)", "Bash(npx prisma migrate dev:*)", "Bash(npx prisma:*)", "<PERSON><PERSON>(true)", "Bash(open http://localhost:3002/users)", "Bash(pgrep:*)", "<PERSON><PERSON>(open:*)", "Bash(npm test)", "Bash(npm test:*)", "Bash(REACT_APP_API_URL=http://localhost:3333/api/v1 PORT=3001 npm start)", "Bash(PORT=4001 npm run start:dev)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(touch:*)", "Bash(npx nest g module:*)", "Bash(npx nest g service:*)", "Bash(npx nest g controller:*)", "Bash(psql:*)", "Bash(npx tsc:*)", "<PERSON><PERSON>(sed:*)", "Bash(perl:*)", "Bash(for i in 7 8 9 10 11 12)", "Bash(do sed -i '' \"$is|ZenCash - Sistema de Log[^/]*_files|landing-assets|g\" landing.html)", "Bash(done)", "Bash(for i in 468 469 470)", "Bash(do sed -i '' \"$is|ZenCash - Sistema de Log[^/]*_files|landing-assets|g\" /Users/<USER>/sistema-cobranca/frontend/public/landing.html)", "Bash(NODE_OPTIONS=\"--max-old-space-size=4096\" PORT=3001 npm start)", "Bash(node:*)", "Bash(git remote add:*)", "Bash(git remote set-url:*)", "Bash(git push:*)", "Bash(PORT=3000 npm run start:dev)", "Bash(PORT=3003 npm run start:dev)", "<PERSON><PERSON>(cat:*)", "Bash(ps:*)", "Bash(NODE_OPTIONS=\"--max-old-space-size=4096\" npm start)", "Bash(ssh-keygen:*)", "Bash(brew install:*)", "<PERSON><PERSON>(scp:*)", "WebFetch(domain:docs.anthropic.com)", "Bash(claude mcp add:*)", "<PERSON><PERSON>(claude mcp:*)", "<PERSON><PERSON>(claude --debug)", "Bash(npx:*)", "Bash(claude --debug \"test\" 2 >& 1)", "Bash(npm ls:*)", "Bash(timeout 5 npx @modelcontextprotocol/server-puppeteer)", "Bash(git checkout:*)", "<PERSON>sh(./test-commission.sh:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(railway link:*)", "Bash(./railway-deploy-automated.sh)", "Bash(railway status:*)", "<PERSON><PERSON>(vercel:*)", "Bash(./fix-build-errors.sh)", "Bash(./fix-remaining-errors.sh:*)", "Bash(./minimal-fix.sh:*)", "Bash(./prepare-for-railway.sh:*)", "Bash(./final-build-fix.sh:*)", "Bash(./railway-final-fix.sh:*)", "Bash(./railway-simple-build.sh)", "Bash(./generate-keys.sh:*)", "Bash(git update-index:*)", "Bash(./build.sh)", "Bash(GENERATE_SOURCEMAP=false CI=false npm run build)", "<PERSON><PERSON>(railway login:*)", "Bash(./railway-frontend-quick-setup.sh:*)", "Bash(railway up:*)", "Bash(npm view:*)", "Bash(railway logs:*)", "Bash(REACT_APP_API_URL=https://zencash-production.up.railway.app/api/v1 REACT_APP_TENANT_ID=************************************ npm run build:railway)", "Bash(./check-deployment.sh)"]}, "enableAllProjectMcpServers": false}